"""create jira_comments table

Revision ID: 20250728_001
Revises: 20250521_001
Create Date: 2025-07-28 13:32:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
import uuid
from datetime import datetime


# revision identifiers, used by Alembic.
revision = '20250728_001'
down_revision = '20250521_001'  # This should be the ID of the last migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        'jira_comments',
        sa.Column('uuid', sa.String(), primary_key=True),
        sa.Column('org_id', sa.String(), nullable=False),
        sa.Column('project_key', sa.String(), nullable=False),
        sa.Column('issue_key', sa.String(), nullable=False),
        sa.Column('comment_date', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()')),
        sa.Column('comment', sa.Text(), nullable=False),
        sa.Column('user_email', sa.String(), nullable=True),
        sa.Column('user_id', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('uuid')
    )
    
    # Create individual indexes
    op.create_index('idx_jira_comments_org_id', 'jira_comments', ['org_id'])
    op.create_index('idx_jira_comments_project_key', 'jira_comments', ['project_key'])
    op.create_index('idx_jira_comments_issue_key', 'jira_comments', ['issue_key'])
    op.create_index('idx_jira_comments_comment_date', 'jira_comments', ['comment_date'])
    op.create_index('idx_jira_comments_user_id', 'jira_comments', ['user_id'])
    
    # Create composite indexes for common query patterns
    op.create_index('idx_jira_comments_org_project', 'jira_comments', ['org_id', 'project_key'])
    op.create_index('idx_jira_comments_issue', 'jira_comments', ['org_id', 'project_key', 'issue_key'])
    op.create_index('idx_jira_comments_user', 'jira_comments', ['org_id', 'user_id'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_jira_comments_org_id')
    op.drop_index('idx_jira_comments_project_key')
    op.drop_index('idx_jira_comments_issue_key')
    op.drop_index('idx_jira_comments_comment_date')
    op.drop_index('idx_jira_comments_user_id')
    op.drop_index('idx_jira_comments_org_project')
    op.drop_index('idx_jira_comments_issue')
    op.drop_index('idx_jira_comments_user')
    
    # Drop table
    op.drop_table('jira_comments')