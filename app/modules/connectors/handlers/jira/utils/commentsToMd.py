"""
Utility for converting Jira comments from JSON format to Markdown.
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import structlog

logger = structlog.get_logger()

class JiraCommentsToMarkdownConverter:
    """
    Converts Jira/Atlassian comments from JSON format to Markdown.
    Handles rich text content including mentions, links, headings, and media.
    Also extracts author and mentioned user information.
    """
    
    def __init__(self):
        """Initialize the converter with empty comment structure."""
        self.comment = {
            "mentioned_users": [],
            "author": {},
            "last_update": {},
            "comment": ""
        }
    
    def convert_comments(self, comments_data: str) -> Tuple[str, List[Dict], List[Dict]]:
        """
        Convert JSON comments data to markdown format and extract user information.
        
        Args:
            comments_data: JSON string containing the comment structure with "comment" key
            
        Returns:
            Tuple of (markdown_string, authors_list, mentioned_users_list)
        """
        try:
            # Parse JSON
            if isinstance(comments_data, str):
                data = json.loads(comments_data)
            else:
                data = comments_data
                
            # Extract comments array from the new structure
            if isinstance(data, dict) and 'comment' in data:
                comment_data = data['comment']
                if 'comments' in comment_data:
                    comments = comment_data['comments']
                else:
                    comments = []
            elif isinstance(data, dict) and 'comments' in data:
                comments = data['comments']
            elif isinstance(data, list):
                comments = data
            else:
                comments = [data]  # Single comment
                
            # Extract user information and build mention map
            return self._extract_user_information(comments)
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON: {e}")
            return f"Error parsing JSON: {e}", [], []
        except Exception as e:
            logger.error(f"Error converting comments: {e}")
            return f"Error converting comments: {e}", [], []
    
    def _extract_user_information(self, comments: List[Dict[str, Any]]):
        """
        Extract author and mentioned user information from all comments.
        
        Args:
            comments: List of comment dictionaries
            
        Returns:
            List of processed comments with author and mention information
        """
        all_comments = []
        for comment in comments:
            # Extract author information
            seen_authors = set()
            seen_mentioned = set()

            self.comment = {
                "mentioned_users": [],
                "author": {},
                "last_author": {},
                "comment": "",
                "created_at": "",
                "updated_at": "",
                "comment_id": ""
            }

            author = comment.get('author', {})
            author_id = author.get('accountId', '')

            updated_author = comment.get("updateAuthor", {})
            updated_author_id = updated_author.get('accountId', '')
            
            if author_id and author_id not in seen_authors:
                author_info = {
                    'email': author.get('emailAddress', ''),
                    'accountId': author_id,
                    'displayName': author.get('displayName', '')
                }
                updated_author_info = {
                    'email': updated_author.get('emailAddress', ''),
                    'accountId': updated_author_id,
                    'displayName': updated_author.get('displayName', '')
                }
                self.comment["author"] = author_info
                self.comment["last_author"] = updated_author_info
                seen_authors.add(author_id)

                body = comment.get('body', {})
            
                # Extract mentioned users from comment body
                self._extract_mentions_from_body(body, seen_mentioned)

                self.comment["comment"] = self._convert_doc_content(body)

                created = comment.get('created', '')
                updated = comment.get('updated', '')
                self.comment["comment_id"] = comment.get('id', '')
                
                # Format timestamp
                self.comment["created_at"] = self._format_timestamp(created)
                self.comment["updated_at"] = self._format_timestamp(updated)

                all_comments.append(self.comment)
        
        return all_comments
    
    def _extract_mentions_from_body(self, body: Dict[str, Any], seen_mentioned: set):
        """
        Recursively extract mentions from comment body content.
        
        Args:
            body: Comment body dictionary
            seen_mentioned: Set of already seen mention IDs
        """
        if not body or 'content' not in body:
            return
            
        content = body.get('content', [])
        for item in content:
            self._extract_mentions_from_content_node(item, seen_mentioned)
    
    def _extract_mentions_from_content_node(self, node: Dict[str, Any], seen_mentioned: set):
        """
        Extract mentions from a content node recursively.
        
        Args:
            node: Content node dictionary
            seen_mentioned: Set of already seen mention IDs
        """
        node_type = node.get('type', '')
        
        if node_type == 'mention':
            attrs = node.get('attrs', {})
            account_id = attrs.get('id', '')
            display_name = attrs.get('text', '')
            
            # Clean up display name (remove @ prefix if present)
            if display_name.startswith('@'):
                display_name = display_name[1:]
            
            if account_id and account_id not in seen_mentioned:
                mention_info = {
                    'name': display_name,
                    'accountId': account_id
                }
                self.comment["mentioned_users"].append(mention_info)
                seen_mentioned.add(account_id)
        
        # Recursively check nested content
        if 'content' in node:
            for child_node in node.get('content', []):
                self._extract_mentions_from_content_node(child_node, seen_mentioned)
    
    def _convert_doc_content(self, doc: Dict[str, Any]) -> str:
        """
        Convert document content to markdown.
        
        Args:
            doc: Document dictionary with content
            
        Returns:
            Markdown string representation
        """
        if not doc or 'content' not in doc:
            return ""
        
        content_parts = []
        for content_item in doc.get('content', []):
            converted = self._convert_content_node(content_item)
            if converted:
                content_parts.append(converted)
        
        return '\n\n'.join(content_parts)
    
    def _convert_content_node(self, node: Dict[str, Any]) -> str:
        """
        Convert a single content node to markdown.
        
        Args:
            node: Content node dictionary
            
        Returns:
            Markdown string for the node
        """
        node_type = node.get('type', '')
        
        if node_type == 'paragraph':
            return self._convert_paragraph(node)
        elif node_type == 'heading':
            return self._convert_heading(node)
        elif node_type == 'mediaSingle':
            return self._convert_media_single(node)
        else:
            # Handle other node types or return text content if available
            return self._extract_text_content(node)
    
    def _convert_paragraph(self, node: Dict[str, Any]) -> str:
        """
        Convert paragraph node to markdown.
        
        Args:
            node: Paragraph node dictionary
            
        Returns:
            Markdown string for the paragraph
        """
        content_parts = []
        
        for content_item in node.get('content', []):
            converted = self._convert_inline_content(content_item)
            if converted:
                content_parts.append(converted)
        
        return ''.join(content_parts)
    
    def _convert_heading(self, node: Dict[str, Any]) -> str:
        """
        Convert heading node to markdown.
        
        Args:
            node: Heading node dictionary
            
        Returns:
            Markdown string for the heading
        """
        level = node.get('attrs', {}).get('level', 1)
        heading_prefix = '#' * min(level + 2, 6)  # Offset by 2 since comment title is ##
        
        content_parts = []
        for content_item in node.get('content', []):
            converted = self._convert_inline_content(content_item)
            if converted:
                content_parts.append(converted)
        
        heading_text = ''.join(content_parts)
        return f"{heading_prefix} {heading_text}"
    
    def _convert_inline_content(self, node: Dict[str, Any]) -> str:
        """
        Convert inline content (text, mentions, links, etc.) to markdown.
        
        Args:
            node: Inline content node dictionary
            
        Returns:
            Markdown string for the inline content
        """
        node_type = node.get('type', '')
        
        if node_type == 'text':
            return node.get('text', '')
        elif node_type == 'mention':
            return self._convert_mention(node)
        elif node_type == 'inlineCard':
            return self._convert_inline_card(node)
        elif node_type == 'hardBreak':
            return '\n'
        else:
            # Extract any text content as fallback
            return self._extract_text_content(node)
    
    def _convert_mention(self, node: Dict[str, Any]) -> str:
        """
        Convert mention node to markdown.
        
        Args:
            node: Mention node dictionary
            
        Returns:
            Markdown string for the mention
        """
        attrs = node.get('attrs', {})
        
        # Try to get display name from text attribute first
        display_name = attrs.get('text', '')
        if display_name.startswith('@'):
            display_name = display_name[1:]  # Remove @ prefix
        
        return f"**@{display_name}**"

    def _convert_inline_card(self, node: Dict[str, Any]) -> str:
        """
        Convert inline card (link) to markdown.
        
        Args:
            node: Inline card node dictionary
            
        Returns:
            Markdown string for the link
        """
        url = node.get('attrs', {}).get('url', '')
        if url:
            # Try to extract a meaningful name from the URL
            try:
                from urllib.parse import urlparse
                parsed = urlparse(url)
                
                # Remove common prefixes and suffixes
                domain = parsed.netloc.lower()
                path = parsed.path.strip('/')
                
                # Extract meaningful parts from the path
                if path:
                    path_parts = [part for part in path.split('/') if part]
                    if len(path_parts) >= 2:
                        # Use the last two parts for most URLs (e.g., user/repo, category/item)
                        name = f"{path_parts[-2]}/{path_parts[-1]}"
                    elif len(path_parts) == 1:
                        # Use the single path part
                        name = path_parts[0]
                    else:
                        # Fallback to domain
                        name = domain.replace('www.', '')
                else:
                    # No path, use domain
                    name = domain.replace('www.', '')
                
                return f"[{name}]({url})"
            except Exception as e:
                logger.error(f"Error parsing URL: {e}")
                # If URL parsing fails, use the full URL as the link text
                return f"[{url}]({url})"
        return ""
    
    def _convert_media_single(self, node: Dict[str, Any]) -> str:
        """
        Convert media node to markdown.
        
        Args:
            node: Media node dictionary
            
        Returns:
            Markdown string for the media
        """
        content = node.get('content', [])
        for item in content:
            if item.get('type') == 'media':
                attrs = item.get('attrs', {})
                alt_text = attrs.get('alt', 'Image')
                media_type = attrs.get('type', 'file')
                
                if media_type == 'file':
                    return f"📎 **Attachment:** {alt_text}"
                else:
                    return f"🖼️ **Media:** {alt_text}"
        return "📎 **Media attachment**"
    
    def _extract_text_content(self, node: Dict[str, Any]) -> str:
        """
        Extract plain text content from any node as fallback.
        
        Args:
            node: Node dictionary
            
        Returns:
            Plain text content
        """
        if isinstance(node, dict):
            if 'text' in node:
                return node['text']
            elif 'content' in node:
                text_parts = []
                for item in node['content']:
                    text_parts.append(self._extract_text_content(item))
                return ''.join(text_parts)
        return ""
    
    def _format_timestamp(self, timestamp: str) -> str:
        """
        Format timestamp for display.
        
        Args:
            timestamp: ISO format timestamp string
            
        Returns:
            Formatted timestamp string
        """
        if not timestamp:
            return ""
        
        try:
            # Parse ISO format with timezone
            # Example: "2025-06-16T07:06:05.672-0700"
            if '+' in timestamp or timestamp.count('-') > 2:
                # Handle timezone
                if '+' in timestamp:
                    dt_part = timestamp.split('+')[0]
                else:
                    # Find the last dash that's part of timezone
                    parts = timestamp.split('-')
                    dt_part = '-'.join(parts[:-1])
                
                dt = datetime.fromisoformat(dt_part.replace('T', ' '))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                dt = datetime.fromisoformat(timestamp.replace('T', ' '))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError as e:
            logger.error(f"Error parsing timestamp: {e}")
            return timestamp  # Return original if parsing fails
