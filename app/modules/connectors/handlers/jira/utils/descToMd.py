"""
Utility for converting Jira/Atlassian document structure to Markdown format.
"""

import json
from typing import Dict, List, Any, Optional
import structlog

logger = structlog.get_logger()

class DocumentConverter:
    """
    A class to convert structured documents (like ProseMirror/Atlassian format) into plain text.
    
    This converter handles various node types including paragraphs, lists, headings, tables,
    code blocks, media elements, and rich text formatting.
    """
    
    def __init__(self):
        """Initialize the DocumentConverter with node processors mapping."""
        self.node_processors = {
            "paragraph": self._process_paragraph,
            "bulletList": self._process_bullet_list,
            "orderedList": self._process_ordered_list,
            "listItem": self._process_list_item,
            "heading": self._process_heading,
            "blockquote": self._process_blockquote,
            "codeBlock": self._process_code_block,
            "text": self._process_text_node,
            "taskList": self._process_task_list,
            "taskItem": self._process_task_item,
            "table": self._process_table,
            "tableRow": self._process_table_row,
            "tableHeader": self._process_table_cell,
            "tableCell": self._process_table_cell,
            "mention": self._process_mention,
            "emoji": self._process_emoji,
            "inlineCard": self._process_inline_card,
            "hardBreak": self._process_hard_break,
            "mediaSingle": self._process_media_single,
            "media": self._process_media,
        }
    
    def convert_to_text(self, doc_data: Dict[str, Any]) -> str:
        """
        Convert a structured document to plain text.
        
        Args:
            doc_data: Dictionary containing the document structure with type, version, and content
            
        Returns:
            Formatted plain text string
        """
        if not doc_data or "content" not in doc_data:
            return ""
        
        return self._process_content_list(doc_data["content"])
    
    def convert_from_json_string(self, json_str: str) -> str:
        """
        Convert a JSON string containing the document structure to plain text.
        
        Args:
            json_str: JSON string containing the document data
            
        Returns:
            Formatted plain text string or error message
        """
        try:
            doc_data = json.loads(json_str)
            return self.convert_to_text(doc_data)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON: {e}")
            return f"Error parsing JSON: {e}"
        except Exception as e:
            logger.error(f"Error converting document: {e}")
            return f"Error converting document: {e}"
    
    def _process_content_list(self, content_list: List[Dict[str, Any]]) -> str:
        """
        Process a list of content nodes and return formatted text.
        
        Args:
            content_list: List of content node dictionaries
            
        Returns:
            Formatted text string
        """
        result = []
        
        for item in content_list:
            text = self._process_content_node(item)
            if text:
                result.append(text)
        
        return "\n".join(result)
    
    def _process_content_node(self, node: Dict[str, Any]) -> str:
        """
        Process a single content node based on its type.
        
        Args:
            node: Content node dictionary
            
        Returns:
            Formatted text for the node
        """
        node_type = node.get("type", "")
        
        # Use the processor mapping for known types
        if node_type in self.node_processors:
            return self.node_processors[node_type](node)
        
        # For unknown types, try to process any nested content
        if "content" in node:
            return self._process_content_list(node["content"])
        
        return ""
    
    def _process_paragraph(self, node: Dict[str, Any]) -> str:
        """
        Process a paragraph node.
        
        Args:
            node: Paragraph node dictionary
            
        Returns:
            Formatted text for the paragraph
        """
        if "content" not in node:
            return ""
        
        text_parts = []
        for content_item in node["content"]:
            text_parts.append(self._process_content_node(content_item))
        
        return "".join(text_parts)
    
    def _process_bullet_list(self, node: Dict[str, Any]) -> str:
        """
        Process a bullet list node.
        
        Args:
            node: Bullet list node dictionary
            
        Returns:
            Formatted text for the bullet list
        """
        if "content" not in node:
            return ""
        
        items = []
        for list_item in node["content"]:
            item_text = self._process_list_item(list_item)
            if item_text:
                items.append(f"• {item_text}")
        
        return "\n".join(items)
    
    def _process_ordered_list(self, node: Dict[str, Any]) -> str:
        """
        Process an ordered list node.
        
        Args:
            node: Ordered list node dictionary
            
        Returns:
            Formatted text for the ordered list
        """
        if "content" not in node:
            return ""
        
        items = []
        start_order = node.get("attrs", {}).get("order", 1)
        
        for i, list_item in enumerate(node["content"], start_order):
            item_text = self._process_list_item(list_item)
            if item_text:
                items.append(f"{i}. {item_text}")
        
        return "\n".join(items)
    
    def _process_list_item(self, node: Dict[str, Any]) -> str:
        """
        Process a list item node.
        
        Args:
            node: List item node dictionary
            
        Returns:
            Formatted text for the list item
        """
        if "content" not in node:
            return ""
        
        content_parts = []
        for content_node in node["content"]:
            if content_node.get("type") == "paragraph":
                content_parts.append(self._process_paragraph(content_node))
            elif content_node.get("type") in ["bulletList", "orderedList"]:
                # Handle nested lists with indentation
                nested_list = self._process_content_node(content_node)
                indented_list = "\n".join(f"  {line}" for line in nested_list.split("\n"))
                content_parts.append(indented_list)
            else:
                content_parts.append(self._process_content_node(content_node))
        
        return "\n".join(content_parts)
    
    def _process_heading(self, node: Dict[str, Any]) -> str:
        """
        Process a heading node.
        
        Args:
            node: Heading node dictionary
            
        Returns:
            Formatted text for the heading
        """
        level = node.get("attrs", {}).get("level", 1)
        text = self._process_content_list(node.get("content", []))
        
        # Create markdown-style heading
        prefix = "#" * level
        return f"{prefix} {text}"
    
    def _process_blockquote(self, node: Dict[str, Any]) -> str:
        """
        Process a blockquote node.
        
        Args:
            node: Blockquote node dictionary
            
        Returns:
            Formatted text for the blockquote
        """
        content = self._process_content_list(node.get("content", []))
        lines = content.split("\n")
        quoted_lines = [f"> {line}" for line in lines]
        return "\n".join(quoted_lines)
    
    def _process_code_block(self, node: Dict[str, Any]) -> str:
        """
        Process a code block node.
        
        Args:
            node: Code block node dictionary
            
        Returns:
            Formatted text for the code block
        """
        language = node.get("attrs", {}).get("language", "")
        content = self._process_content_list(node.get("content", []))
        
        return f"```{language}\n{content}\n```"
    
    def _process_text_node(self, node: Dict[str, Any]) -> str:
        """
        Process a text node, applying formatting marks.
        
        Args:
            node: Text node dictionary
            
        Returns:
            Formatted text with applied marks
        """
        if node.get("type") != "text":
            return ""
        
        text = node.get("text", "")
        marks = node.get("marks", [])
        
        # Apply formatting marks
        for mark in marks:
            text = self._apply_text_mark(text, mark)
        
        return text
    
    def _apply_text_mark(self, text: str, mark: Dict[str, Any]) -> str:
        """
        Apply a single formatting mark to text.
        
        Args:
            text: Text to format
            mark: Mark dictionary with type and attributes
            
        Returns:
            Formatted text with the mark applied
        """
        mark_type = mark.get("type", "")
        
        if mark_type in ["strong", "bold"]:
            return f"**{text}**"
        elif mark_type in ["em", "italic"]:
            return f"*{text}*"
        elif mark_type == "code":
            return f"`{text}`"
        elif mark_type == "underline":
            return f"_{text}_"
        elif mark_type == "strike":
            return f"~~{text}~~"
        elif mark_type == "link":
            href = mark.get("attrs", {}).get("href", "")
            title = mark.get("attrs", {}).get("title", "")
            if title:
                return f"[{text}]({href} \"{title}\")"
            else:
                return f"[{text}]({href})"
        elif mark_type == "textColor":
            color = mark.get("attrs", {}).get("color", "")
            return f"<span style=\"color: {color}\">{text}</span>"
        elif mark_type == "subsup":
            subsup_type = mark.get("attrs", {}).get("type", "")
            if subsup_type == "sup":
                return f"{text}^"
            elif subsup_type == "sub":
                return f"{text}_"
        elif mark_type == "border":
            size = mark.get("attrs", {}).get("size", "")
            color = mark.get("attrs", {}).get("color", "")
            return f"[{text} - Border: {size}px {color}]"
        
        return text
    
    def _process_task_list(self, node: Dict[str, Any]) -> str:
        """
        Process a task list node.
        
        Args:
            node: Task list node dictionary
            
        Returns:
            Formatted text for the task list
        """
        if "content" not in node:
            return ""
        
        items = []
        for task_item in node["content"]:
            item_text = self._process_task_item(task_item)
            if item_text:
                items.append(item_text)
        
        return "\n".join(items)
    
    def _process_task_item(self, node: Dict[str, Any]) -> str:
        """
        Process a task item node.
        
        Args:
            node: Task item node dictionary
            
        Returns:
            Formatted text for the task item
        """
        if "content" not in node:
            return ""
        
        # Get task state
        state = node.get("attrs", {}).get("state", "TODO")
        checkbox = "☐" if state == "TODO" else "☑"
        
        # Process content
        content_parts = []
        for content_item in node["content"]:
            content_parts.append(self._process_content_node(content_item))
        
        content_text = "".join(content_parts)
        return f"  {checkbox} {content_text}"
    
    def _process_table(self, node: Dict[str, Any]) -> str:
        """
        Process a table node.
        
        Args:
            node: Table node dictionary
            
        Returns:
            Formatted text for the table
        """
        if "content" not in node:
            return ""
        
        rows = []
        for row_node in node["content"]:
            row_text = self._process_table_row(row_node)
            if row_text:
                rows.append(row_text)
        
        if not rows:
            return ""
        
        # Create markdown table format
        result = []
        for i, row in enumerate(rows):
            result.append(row)
            # Add separator after header row
            if i == 0:
                col_count = row.count("|") - 1
                separator = "|" + "---|" * col_count
                result.append(separator)
        
        return "\n".join(result)
    
    def _process_table_row(self, node: Dict[str, Any]) -> str:
        """
        Process a table row node.
        
        Args:
            node: Table row node dictionary
            
        Returns:
            Formatted text for the table row
        """
        if "content" not in node:
            return ""
        
        cells = []
        for cell_node in node["content"]:
            cell_text = self._process_content_node(cell_node)
            # Clean up cell text and escape pipes
            cell_text = cell_text.replace("|", "\\|").replace("\n", " ").strip()
            cells.append(cell_text)
        
        return "| " + " | ".join(cells) + " |"
    
    def _process_table_cell(self, node: Dict[str, Any]) -> str:
        """
        Process a table cell or header node.
        
        Args:
            node: Table cell node dictionary
            
        Returns:
            Formatted text for the table cell
        """
        if "content" not in node:
            return ""
        
        content = self._process_content_list(node["content"])
        
        # Handle background color if present
        background = node.get("attrs", {}).get("background", "")
        if background:
            content = f"[{content}] (bg: {background})"
        
        return content
    
    def _process_mention(self, node: Dict[str, Any]) -> str:
        """
        Process a mention node.
        
        Args:
            node: Mention node dictionary
            
        Returns:
            Formatted text for the mention
        """
        attrs = node.get("attrs", {})
        text = attrs.get("text", "@unknown")
        return text
    
    def _process_emoji(self, node: Dict[str, Any]) -> str:
        """
        Process an emoji node.
        
        Args:
            node: Emoji node dictionary
            
        Returns:
            Formatted text for the emoji
        """
        attrs = node.get("attrs", {})
        emoji_text = attrs.get("text", "")
        short_name = attrs.get("shortName", "")
        
        return emoji_text if emoji_text else short_name
    
    def _process_inline_card(self, node: Dict[str, Any]) -> str:
        """
        Process an inline card node (typically a URL).
        
        Args:
            node: Inline card node dictionary
            
        Returns:
            Formatted text for the inline card
        """
        attrs = node.get("attrs", {})
        url = attrs.get("url", "")
        return f"[{url}]({url})"
    
    def _process_hard_break(self, node: Dict[str, Any]) -> str:
        """
        Process a hard break node.
        
        Args:
            node: Hard break node dictionary
            
        Returns:
            Newline character
        """
        return "\n"
    
    def _process_media_single(self, node: Dict[str, Any]) -> str:
        """
        Process a media single node (media container).
        
        Args:
            node: Media single node dictionary
            
        Returns:
            Formatted text for the media container
        """
        if "content" not in node:
            return ""
        
        attrs = node.get("attrs", {})
        width = attrs.get("width", "")
        layout = attrs.get("layout", "")
        
        media_content = self._process_content_list(node["content"])
        
        layout_info = f" (Layout: {layout}" + (f", Width: {width}%" if width else "") + ")"
        return f"[Media{layout_info}]\n{media_content}"
    
    def _process_media(self, node: Dict[str, Any]) -> str:
        """
        Process a media node (image, file, etc.).
        
        Args:
            node: Media node dictionary
            
        Returns:
            Formatted text for the media
        """
        attrs = node.get("attrs", {})
        media_type = attrs.get("type", "media")
        alt_text = attrs.get("alt", "")
        media_id = attrs.get("id", "")
        width = attrs.get("width", "")
        height = attrs.get("height", "")
        
        # Process any marks (like borders)
        marks = node.get("marks", [])
        marks_info = ""
        for mark in marks:
            if mark.get("type") == "border":
                size = mark.get("attrs", {}).get("size", "")
                color = mark.get("attrs", {}).get("color", "")
                marks_info = f" (Border: {size}px, Color: {color})"
        
        dimensions = ""
        if width and height:
            dimensions = f" - {width}x{height}"
        
        return f"[{media_type.title()}: {alt_text or media_id}{dimensions}{marks_info}]"
    
    def add_custom_processor(self, node_type: str, processor_func):
        """
        Add a custom processor for a specific node type.
        
        Args:
            node_type: The type of node to process
            processor_func: Function that takes a node dict and returns a string
        """
        self.node_processors[node_type] = processor_func
    
    def remove_processor(self, node_type: str):
        """
        Remove a processor for a specific node type.
        
        Args:
            node_type: The type of node processor to remove
        """
        if node_type in self.node_processors:
            del self.node_processors[node_type]
