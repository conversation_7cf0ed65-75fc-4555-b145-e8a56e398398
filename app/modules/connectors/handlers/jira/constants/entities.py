"""
Jira Connector Entity Type Definitions

This module defines all entity types for the Jira connector.
"""

from enum import Enum


class EntityType(Enum):
    """
    Jira entity types for knowledge graph nodes.
    
    Define all possible node types that can be created in the knowledge graph
    for Jira data.
    """
    
    # User entities
    JIRA_USER = "JiraUser"
    
    # Task tracking entities
    ASSIGNED_TASKS = "Assigned_tasks"
    REPORTED_TASKS = "Reported_tasks"
    ACCESS_PROJECTS = "Access_projects"
    
    # Project entities
    PROJECT = "Project"
    BACKLOG = "Backlog"
    
    # Issue tracking entities
    ISSUE = "Issue"
    SPRINT = "Sprint"


def get_all_entity_types():
    """
    Returns all Jira entity types for connector registration.
    
    Returns:
        set: Set of all entity type string values
    """
    return {e.value for e in EntityType}


def get_user_entity_types():
    """
    Returns user-related entity types.
    
    Returns:
        set: Set of user entity type string values
    """
    return {
        EntityType.JIRA_USER.value
    }


def get_project_entity_types():
    """
    Returns project-related entity types.
    
    Returns:
        set: Set of project entity type string values
    """
    return {
        EntityType.PROJECT.value,
        EntityType.BACKLOG.value
    }


def get_issue_entity_types():
    """
    Returns issue-related entity types.
    
    Returns:
        set: Set of issue entity type string values
    """
    return {
        EntityType.ISSUE.value,
        EntityType.SPRINT.value
    }


def get_task_tracking_entity_types():
    """
    Returns task tracking-related entity types.
    
    Returns:
        set: Set of task tracking entity type string values
    """
    return {
        EntityType.ASSIGNED_TASKS.value,
        EntityType.REPORTED_TASKS.value,
        EntityType.ACCESS_PROJECTS.value
    }


def is_user_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents a user.
    
    Args:
        entity_type: The entity type string
        
    Returns:
        bool: True if it's a user entity, False otherwise
    """
    return entity_type in get_user_entity_types()


def is_project_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents a project.
    
    Args:
        entity_type: The entity type string
        
    Returns:
        bool: True if it's a project entity, False otherwise
    """
    return entity_type in get_project_entity_types()


def is_issue_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents an issue.
    
    Args:
        entity_type: The entity type string
        
    Returns:
        bool: True if it's an issue entity, False otherwise
    """
    return entity_type in get_issue_entity_types()