"""
Jira Connector Relationship Type Definitions

This module defines all relationship types for the Jira connector.
"""

from enum import Enum


class RelationshipType(Enum):
    """
    Jira relationship types for knowledge graph edges.
    
    Define all possible edge types that can be created in the knowledge graph
    for Jira data relationships.
    """
    
    # Project relationships
    HAS_ISSUE = "HAS_ISSUE"
    HAS_SPRINT = "HAS_SPRINT"
    
    # User relationships
    HAS_USER = "HAS_USER"
    HAS_ACCOUNT = "HAS_ACCOUNT"
    
    # Issue assignment relationships
    REPORTED_TO = "REPORTED_TO"
    ASSIGNED_TO = "ASSIGNED_TO"
    
    # Source relationships
    HAS_PROJECT = "HAS_PROJECT"
    
    # Access relationships
    HAS_ACCESS = "HAS_ACCESS"
    
    # Task tracking relationships
    HAS_ASSIGNED_TASKS = "HAS_ASSIGNED_TASKS"
    HAS_REPORTED_TASKS = "HAS_REPORTED_TASKS"
    
    # Hierarchical relationships
    HAS_PARENT = "HAS_PARENT"
    
    # Sprint relationships
    IN_SPRINT = "IN_SPRINT"


def get_all_relationship_types():
    """
    Returns all Jira relationship types for connector registration.
    
    Returns:
        set: Set of all relationship type string values
    """
    return {r.value for r in RelationshipType}


def get_project_relationship_types():
    """
    Returns project-related relationship types.
    
    Returns:
        set: Set of project relationship type string values
    """
    return {
        RelationshipType.HAS_ISSUE.value,
        RelationshipType.HAS_SPRINT.value
    }


def get_user_relationship_types():
    """
    Returns user-related relationship types.
    
    Returns:
        set: Set of user relationship type string values
    """
    return {
        RelationshipType.HAS_USER.value,
        RelationshipType.HAS_ACCOUNT.value,
        RelationshipType.REPORTED_TO.value,
        RelationshipType.ASSIGNED_TO.value
    }


def get_source_relationship_types():
    """
    Returns source-related relationship types.
    
    Returns:
        set: Set of source relationship type string values
    """
    return {
        RelationshipType.HAS_PROJECT.value,
        RelationshipType.HAS_USER.value
    }


def get_access_relationship_types():
    """
    Returns access-related relationship types.
    
    Returns:
        set: Set of access relationship type string values
    """
    return {
        RelationshipType.HAS_ACCESS.value
    }


def get_task_tracking_relationship_types():
    """
    Returns task tracking-related relationship types.
    
    Returns:
        set: Set of task tracking relationship type string values
    """
    return {
        RelationshipType.HAS_ASSIGNED_TASKS.value,
        RelationshipType.HAS_REPORTED_TASKS.value
    }


def get_hierarchical_relationship_types():
    """
    Returns hierarchical relationship types.
    
    Returns:
        set: Set of hierarchical relationship type string values
    """
    return {
        RelationshipType.HAS_PARENT.value
    }


def get_sprint_relationship_types():
    """
    Returns sprint-related relationship types.
    
    Returns:
        set: Set of sprint relationship type string values
    """
    return {
        RelationshipType.IN_SPRINT.value,
        RelationshipType.HAS_SPRINT.value
    }


def is_bidirectional_relationship(relationship_type: str) -> bool:
    """
    Check if a relationship type is bidirectional.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        bool: True if bidirectional, False otherwise
    """
    # Currently, no bidirectional relationships are defined for Jira
    bidirectional_relationships = set()
    return relationship_type in bidirectional_relationships


def get_relationship_category(relationship_type: str) -> str:
    """
    Get the category of a relationship type.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        str: The category name
    """
    if relationship_type in get_project_relationship_types():
        return "project"
    elif relationship_type in get_user_relationship_types():
        return "user"
    elif relationship_type in get_source_relationship_types():
        return "source"
    elif relationship_type in get_access_relationship_types():
        return "access"
    elif relationship_type in get_task_tracking_relationship_types():
        return "task_tracking"
    elif relationship_type in get_hierarchical_relationship_types():
        return "hierarchical"
    elif relationship_type in get_sprint_relationship_types():
        return "sprint"
    else:
        return "other"