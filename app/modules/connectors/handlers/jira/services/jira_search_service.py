"""
Jira Natural Language Search Service

This module provides a natural language search interface for Jira data
that intelligently routes queries to the appropriate database(s) based on
inferred intent(s).
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime

from app.utils.pinecone.pinecone_service import PineconeService
from app.modules.connectors.handlers.jira.repository.jira_comments_repository import JiraCommentsRepository
from app.services.neo4j_service import execute_read_query
from app.utils.llm.client import LLMClient

logger = logging.getLogger(__name__)

class SearchIntent:
    """Enum-like class for search intent types"""
    SEMANTIC = "semantic"  # Search for ticket summaries or descriptions (Pinecone)
    COMMENTS = "comments"  # Search for comments or discussion context (PostgreSQL)
    METADATA = "metadata"  # Search for structured metadata (Neo4j)
    HYBRID = "hybrid"      # Combination of multiple intents

class JiraSearchService:
    """
    Natural language search service for Jira data.
    
    This service intelligently routes queries to one or more of three databases:
    1. Pinecone (Vector DB): Stores semantic embeddings of Jira ticket title and description
    2. PostgreSQL (PG DB): Stores comments on Jira tickets
    3. Neo4j (Graph DB): Stores structured and relational data
    """
    
    def __init__(self):
        """Initialize the search service with necessary repositories and services."""
        self.pinecone_service = PineconeService()
        self.comments_repository = JiraCommentsRepository()
        self.llm_client = LLMClient()
        
        # Initialize adapters
        from app.modules.connectors.handlers.jira.services.search.vector_search_adapter import VectorSearchAdapter
        from app.modules.connectors.handlers.jira.services.search.sql_adapter import SQLAdapter
        from app.modules.connectors.handlers.jira.services.search.cypher_adapter import CypherAdapter
        from app.modules.connectors.handlers.jira.services.search.intent_parser import IntentParser
        from app.modules.connectors.handlers.jira.services.search.query_planner import QueryPlanner
        from app.modules.connectors.handlers.jira.services.search.response_aggregator import ResponseAggregator
        
        self.vector_adapter = VectorSearchAdapter(self.pinecone_service)
        self.sql_adapter = SQLAdapter(self.comments_repository)
        self.cypher_adapter = CypherAdapter()
        self.intent_parser = IntentParser()
        self.query_planner = QueryPlanner()
        self.response_aggregator = ResponseAggregator()
    
    def search(self, query_text: str, organisation_id: str, user_id: str = None, 
               project_key: Optional[str] = None, issue_key: Optional[str] = None,
               max_results: int = 10) -> Dict[str, Any]:
        """
        Main search method that accepts a natural language query and returns relevant results.
        
        Args:
            query_text: Natural language query text
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            max_results: Maximum number of results to return
            
        Returns:
            Search results with metadata
        """
        try:
            start_time = datetime.now()
            
            # Parse intent from query
            intents, confidence = self.intent_parser.parse_intent(query_text)
            logger.info(f"Detected intents: {intents} with confidence {confidence}")
            
            # Generate query plans for each intent
            query_plans = self.query_planner.generate_query_plans(
                query_text=query_text,
                intents=intents,
                organisation_id=organisation_id,
                user_id=user_id,
                project_key=project_key,
                issue_key=issue_key
            )
            
            # Execute queries based on intent
            results = {}
            
            if SearchIntent.SEMANTIC in intents:
                vector_results = self.vector_adapter.search(
                    query_plans.get(SearchIntent.SEMANTIC, {}),
                    max_results=max_results
                )
                results[SearchIntent.SEMANTIC] = vector_results
            
            if SearchIntent.COMMENTS in intents:
                comment_results = self.sql_adapter.search(
                    query_plans.get(SearchIntent.COMMENTS, {}),
                    max_results=max_results
                )
                results[SearchIntent.COMMENTS] = comment_results
            
            if SearchIntent.METADATA in intents:
                metadata_results = self.cypher_adapter.search(
                    query_plans.get(SearchIntent.METADATA, {}),
                    max_results=max_results
                )
                results[SearchIntent.METADATA] = metadata_results
            
            # If no results or low confidence, fall back to vector search
            if not any(results.values()) or confidence < 0.5:
                logger.info(f"Falling back to vector search due to no results or low confidence ({confidence})")
                fallback_plan = self.query_planner.generate_fallback_plan(
                    query_text=query_text,
                    organisation_id=organisation_id,
                    user_id=user_id,
                    project_key=project_key,
                    issue_key=issue_key
                )
                vector_results = self.vector_adapter.search(
                    fallback_plan,
                    max_results=max_results
                )
                results["fallback"] = vector_results
            
            # Aggregate results
            aggregated_results = self.response_aggregator.aggregate(
                query_text=query_text,
                results=results,
                max_results=max_results
            )
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'query': query_text,
                'intents': list(intents),
                'results': aggregated_results,
                'total_count': len(aggregated_results),
                'execution_time_ms': execution_time,
                'sources': list(results.keys())
            }
            
        except Exception as e:
            logger.error(f"Error in Jira search: {str(e)}")
            return {
                'success': False,
                'query': query_text,
                'error': str(e),
                'results': []
            }
    
    def search_by_intent(self, query_text: str, intent: str, organisation_id: str, 
                        user_id: Optional[str] = None, project_key: Optional[str] = None, 
                        issue_key: Optional[str] = None, max_results: int = 10) -> Dict[str, Any]:
        """
        Search with a specific intent, bypassing intent detection.
        
        Args:
            query_text: Natural language query text
            intent: Specific intent to use (semantic, comments, metadata)
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            max_results: Maximum number of results to return
            
        Returns:
            Search results with metadata
        """
        try:
            start_time = datetime.now()
            
            # Generate query plan for the specific intent
            query_plan = self.query_planner.generate_query_plan(
                query_text=query_text,
                intent=intent,
                organisation_id=organisation_id,
                user_id=user_id,
                project_key=project_key,
                issue_key=issue_key
            )
            
            # Execute query based on intent
            if intent == SearchIntent.SEMANTIC:
                results = self.vector_adapter.search(query_plan, max_results=max_results)
            elif intent == SearchIntent.COMMENTS:
                results = self.sql_adapter.search(query_plan, max_results=max_results)
            elif intent == SearchIntent.METADATA:
                results = self.cypher_adapter.search(query_plan, max_results=max_results)
            else:
                return {
                    'success': False,
                    'query': query_text,
                    'error': f"Invalid intent: {intent}",
                    'results': []
                }
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'query': query_text,
                'intent': intent,
                'results': results,
                'total_count': len(results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Error in Jira search by intent: {str(e)}")
            return {
                'success': False,
                'query': query_text,
                'intent': intent,
                'error': str(e),
                'results': []
            }