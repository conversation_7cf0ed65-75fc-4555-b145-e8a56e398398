"""
Client for interacting with the Jira API.
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JiraClient:
    """
    Client for interacting with the Jira API.
    
    Provides methods for authenticating and making requests to the Jira REST API.
    Handles authentication, error handling, and response parsing.
    """
    
    def __init__(self, base_url: str, username: str = None, api_token: str = None, timeout: int = 30):
        """
        Initialize the Jira client.
        
        Args:
            base_url: Base URL of the Jira instance (e.g., https://your-domain.atlassian.net)
            username: Jira username or email
            api_token: Jira API token
            timeout (int): Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/rest/api/3"
        self.timeout = timeout

        self.username = username
        self.api_token = api_token
        self.session = requests.Session()
        
        # Set up authentication if credentials are provided
        if username and api_token:
            self.session.auth = (username, api_token)
        
        # Set default headers
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'OrganisationService/1.0 (Jira Connector)'
        })
        
        # Test connection
        self.test_connection()
    
    def test_connection(self) -> bool:
        """
        Test the connection to Jira API.
        
        Returns:
            bool: True if connection is successful, raises an exception otherwise
        """
        try:
            response = self.session.get(f"{self.api_base}/myself", timeout=self.timeout)
            response.raise_for_status()
            logger.info(f"Successfully connected to Jira as {response.json().get('displayName')}")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to connect to Jira: {str(e)}")
            raise
    
    def _handle_rate_limiting(self, response: requests.Response) -> None:
        """
        Handle rate limiting by waiting if necessary.
        
        Args:
            response (requests.Response): The response to check for rate limiting
        """
        if response.status_code == 429:  # Too Many Requests
            retry_after = int(response.headers.get("Retry-After", 60))
            logger.warning(f"Rate limit hit. Waiting for {retry_after} seconds")
            time.sleep(retry_after)
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        max_retries: int = 3
    ) -> Dict[str, Any]:
        """
        Make a request to the Jira API with retry logic.
        
        Args:
            method (str): HTTP method (get, post, put, delete)
            endpoint (str): API endpoint (without base URL)
            params (Optional[Dict[str, Any]]): Query parameters
            data (Optional[Dict[str, Any]]): Request body for POST/PUT
            max_retries (int): Maximum number of retries for rate limiting
        
        Returns:
            Dict[str, Any]: JSON response from the API
        """
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        retries = 0
        
        while retries <= max_retries:
            try:
                if method.lower() == "get":
                    response = self.session.get(url, params=params, timeout=self.timeout)
                elif method.lower() == "post":
                    response = self.session.post(url, params=params, json=data, timeout=self.timeout)
                elif method.lower() == "put":
                    response = self.session.put(url, params=params, json=data, timeout=self.timeout)
                elif method.lower() == "delete":
                    response = self.session.delete(url, params=params, timeout=self.timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                if response.status_code == 429:  # Too Many Requests
                    self._handle_rate_limiting(response)
                    retries += 1
                    continue
                
                response.raise_for_status()
                
                if response.status_code == 204:  # No Content
                    return {}
                
                return response.json()
            
            except requests.exceptions.RequestException as e:
                if retries >= max_retries:
                    logger.error(f"Failed after {max_retries} retries: {str(e)}")
                    raise
                retries += 1
                logger.warning(f"Request failed, retrying ({retries}/{max_retries}): {str(e)}")
                time.sleep(2 ** retries)  # Exponential backoff
    
    # Projects
    
    def get_projects(self, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get a list of all projects.
        
        Args:
            params (Optional[Dict[str, Any]]): Additional query parameters
        
        Returns:
            List[Dict[str, Any]]: List of projects
        """
        return self._make_request("get", "project", params=params)
    
    def get_project(self, project_id_or_key: str) -> Dict[str, Any]:
        """
        Get details of a specific project.
        
        Args:
            project_id_or_key (str): Project ID or key
        
        Returns:
            Dict[str, Any]: Project details
        """
        return self._make_request("get", f"project/{project_id_or_key}")
    
    def get_project_components(self, project_id_or_key: str) -> List[Dict[str, Any]]:
        """
        Get components for a specific project.
        
        Args:
            project_id_or_key (str): Project ID or key
        
        Returns:
            List[Dict[str, Any]]: List of project components
        """
        return self._make_request("get", f"project/{project_id_or_key}/components")
    
    def get_project_versions(self, project_id_or_key: str) -> List[Dict[str, Any]]:
        """
        Get versions for a specific project.
        
        Args:
            project_id_or_key (str): Project ID or key
        
        Returns:
            List[Dict[str, Any]]: List of project versions
        """
        return self._make_request("get", f"project/{project_id_or_key}/versions")
    
    # Issues
    
    def search_issues(
        self, 
        jql: str, 
        start_at: int = 0, 
        max_results: int = 50,
        fields: Optional[List[str]] = None,
        expand: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Search for issues using JQL (Jira Query Language).
        
        Args:
            jql (str): JQL query string
            start_at (int): Index of the first result
            max_results (int): Maximum number of results to return (max 100)
            fields (Optional[List[str]]): List of fields to include
            expand (Optional[List[str]]): List of fields to expand
        
        Returns:
            Dict[str, Any]: Search results containing issues
        """
        params = {
            "jql": jql,
            "startAt": start_at,
            "maxResults": max_results
        }
        
        if fields:
            params["fields"] = ",".join(fields)
        
        if expand:
            params["expand"] = ",".join(expand)
        
        return self._make_request("get", "search", params=params)
    
    def get_all_issues(
        self, 
        jql: str, 
        fields: Optional[List[str]] = None,
        expand: Optional[List[str]] = None,
        max_results_per_page: int = 100,
        max_pages: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get all issues matching a JQL query, handling pagination.
        
        Args:
            jql (str): JQL query string
            fields (Optional[List[str]]): List of fields to include
            expand (Optional[List[str]]): List of fields to expand
            max_results_per_page (int): Maximum results per page (max 100)
            max_pages (int): Maximum number of pages to fetch
        
        Returns:
            List[Dict[str, Any]]: List of all matching issues
        """
        all_issues = []
        start_at = 0
        page = 1
        
        while page <= max_pages:
            results = self.search_issues(
                jql=jql,
                start_at=start_at,
                max_results=max_results_per_page,
                fields=fields,
                expand=expand
            )
            
            issues = results.get("issues", [])
            all_issues.extend(issues)
            
            total = results.get("total", 0)
            if start_at + len(issues) >= total:
                break
            
            start_at += len(issues)
            page += 1
            
            # Avoid hitting rate limits
            time.sleep(0.5)
        
        return all_issues
    
    def get_issue(
        self, 
        issue_id_or_key: str,
        fields: Optional[List[str]] = None,
        expand: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get details of a specific issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            fields (Optional[List[str]]): List of fields to include
            expand (Optional[List[str]]): List of fields to expand
        
        Returns:
            Dict[str, Any]: Issue details
        """
        params = {}
        
        if fields:
            params["fields"] = ",".join(fields)
        
        if expand:
            params["expand"] = ",".join(expand)
        
        return self._make_request("get", f"issue/{issue_id_or_key}", params=params)
    
    def create_issue(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new issue.
        
        Args:
            data (Dict[str, Any]): Issue data including project, summary, etc.
        
        Returns:
            Dict[str, Any]: Created issue details
        """
        return self._make_request("post", "issue", data=data)
    
    def update_issue(self, issue_id_or_key: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            data (Dict[str, Any]): Updated issue data
        
        Returns:
            Dict[str, Any]: Updated issue details
        """
        return self._make_request("put", f"issue/{issue_id_or_key}", data=data)
    
    def delete_issue(self, issue_id_or_key: str) -> None:
        """
        Delete an issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
        """
        self._make_request("delete", f"issue/{issue_id_or_key}")
    
    # Comments
    
    def get_comments(self, issue_id_or_key: str) -> Dict[str, Any]:
        """
        Get all comments for an issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
        
        Returns:
            Dict[str, Any]: Comments data
        """
        return self._make_request("get", f"issue/{issue_id_or_key}/comment")
    
    def get_comment(self, issue_id_or_key: str, comment_id: str) -> Dict[str, Any]:
        """
        Get a specific comment.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            comment_id (str): Comment ID
        
        Returns:
            Dict[str, Any]: Comment details
        """
        return self._make_request("get", f"issue/{issue_id_or_key}/comment/{comment_id}")
    
    def add_comment(self, issue_id_or_key: str, body: str) -> Dict[str, Any]:
        """
        Add a comment to an issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            body (str): Comment text
        
        Returns:
            Dict[str, Any]: Created comment details
        """
        data = {
            "body": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": body
                            }
                        ]
                    }
                ]
            }
        }
        return self._make_request("post", f"issue/{issue_id_or_key}/comment", data=data)
    
    def update_comment(self, issue_id_or_key: str, comment_id: str, body: str) -> Dict[str, Any]:
        """
        Update an existing comment.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            comment_id (str): Comment ID
            body (str): Updated comment text
        
        Returns:
            Dict[str, Any]: Updated comment details
        """
        data = {
            "body": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": body
                            }
                        ]
                    }
                ]
            }
        }
        return self._make_request("put", f"issue/{issue_id_or_key}/comment/{comment_id}", data=data)
    
    def delete_comment(self, issue_id_or_key: str, comment_id: str) -> None:
        """
        Delete a comment.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            comment_id (str): Comment ID
        """
        self._make_request("delete", f"issue/{issue_id_or_key}/comment/{comment_id}")
    
    # Users
    
    def get_myself(self) -> Dict[str, Any]:
        """
        Get information about the authenticated user.
        
        Returns:
            Dict[str, Any]: User details
        """
        return self._make_request("get", "myself")
    
    def find_users(self, query: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """
        Find users by query string.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of results to return
        
        Returns:
            List[Dict[str, Any]]: List of matching users
        """
        params = {
            "query": query,
            "maxResults": max_results
        }
        return self._make_request("get", "user/search", params=params)
    
    def get_user_by_account_id(self, account_id: str) -> Dict[str, Any]:
        """
        Get user details by account ID.
        
        Args:
            account_id (str): The user's account ID
        
        Returns:
            Dict[str, Any]: User details including displayName, emailAddress, etc.
            
        Raises:
            ValueError: If account_id is empty or None
            requests.exceptions.HTTPError:
                - 404: If user with the given account ID doesn't exist
                - 403: If you don't have permission to view this user
                - 401: If authentication fails
        """
        if not account_id:
            logger.error("Account ID cannot be empty")
            raise ValueError("Account ID cannot be empty")
            
        logger.info(f"Getting user details for account ID: {account_id}")
        params = {
            "accountId": account_id
        }
        
        try:
            user_data = self._make_request("get", "user", params=params)
            logger.info(f"Successfully retrieved user details for account ID: {account_id}")
            return user_data
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                logger.error(f"User with account ID {account_id} not found")
                raise
            elif e.response.status_code == 403:
                logger.error(f"Permission denied to view user with account ID {account_id}")
                raise
            elif e.response.status_code == 401:
                logger.error("Authentication failed when retrieving user details")
                raise
            else:
                logger.error(f"HTTP error retrieving user details for account ID {account_id}: {str(e)}")
                raise
        except Exception as e:
            logger.error(f"Error retrieving user details for account ID {account_id}: {str(e)}")
            raise
    
    # Attachments
    
    def get_attachments(self, issue_id_or_key: str) -> List[Dict[str, Any]]:
        """
        Get all attachments for an issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
        
        Returns:
            List[Dict[str, Any]]: List of attachments
        """
        issue = self.get_issue(issue_id_or_key, fields=["attachment"])
        return issue.get("fields", {}).get("attachment", [])
    
    def get_attachment(self, attachment_id: str) -> Dict[str, Any]:
        """
        Get attachment metadata.
        
        Args:
            attachment_id (str): Attachment ID
        
        Returns:
            Dict[str, Any]: Attachment metadata
        """
        return self._make_request("get", f"attachment/{attachment_id}")
    
    def download_attachment(self, attachment_id: str) -> bytes:
        """
        Download attachment content.
        
        Args:
            attachment_id (str): Attachment ID
        
        Returns:
            bytes: Attachment content
        """
        attachment = self.get_attachment(attachment_id)
        content_url = attachment.get("content")
        
        if not content_url:
            raise ValueError(f"No content URL found for attachment {attachment_id}")
        
        response = self.session.get(content_url, timeout=self.timeout)
        response.raise_for_status()
        return response.content
    
    # Workflows
    
    def get_workflows(self) -> List[Dict[str, Any]]:
        """
        Get all workflows.
        
        Returns:
            List[Dict[str, Any]]: List of workflows
        """
        return self._make_request("get", "workflow")
    
    def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get a specific workflow.
        
        Args:
            workflow_id (str): Workflow ID
        
        Returns:
            Dict[str, Any]: Workflow details
        """
        return self._make_request("get", f"workflow/{workflow_id}")
    
    # Transitions
    
    def get_transitions(self, issue_id_or_key: str) -> Dict[str, Any]:
        """
        Get available transitions for an issue.
        
        Args:
            issue_id_or_key (str): Issue ID or key
        
        Returns:
            Dict[str, Any]: Available transitions
        """
        return self._make_request("get", f"issue/{issue_id_or_key}/transitions")
    
    def transition_issue(
        self, 
        issue_id_or_key: str, 
        transition_id: str,
        comment: Optional[str] = None,
        fields: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Transition an issue to a new status.
        
        Args:
            issue_id_or_key (str): Issue ID or key
            transition_id (str): Transition ID
            comment (Optional[str]): Comment to add with the transition
            fields (Optional[Dict[str, Any]]): Fields to update during transition
        """
        data = {
            "transition": {
                "id": transition_id
            }
        }
        
        if comment:
            data["update"] = {
                "comment": [
                    {
                        "add": {
                            "body": {
                                "type": "doc",
                                "version": 1,
                                "content": [
                                    {
                                        "type": "paragraph",
                                        "content": [
                                            {
                                                "type": "text",
                                                "text": comment
                                            }
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        
        if fields:
            data["fields"] = fields
        
        self._make_request("post", f"issue/{issue_id_or_key}/transitions", data=data)
    
    # Boards (Agile)
    
    def get_boards(
        self, 
        project_key: Optional[str] = None,
        type: Optional[str] = None,
        start_at: int = 0,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Get all boards.
        
        Args:
            project_key (Optional[str]): Filter by project key
            type (Optional[str]): Filter by board type (scrum, kanban)
            start_at (int): Index of the first result
            max_results (int): Maximum number of results to return
        
        Returns:
            Dict[str, Any]: Boards data
        """
        # Boards are part of the Jira Software REST API, not the core Jira REST API
        # We need to use a different base URL for this request
        agile_api_base = f"{self.base_url}/rest/agile/1.0"
        url = f"{agile_api_base}/board"
        
        params = {
            "startAt": start_at,
            "maxResults": max_results
        }
        
        if project_key:
            params["projectKeyOrId"] = project_key
        
        if type:
            params["type"] = type
        
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting boards: {str(e)}")
            raise
    
    def get_board(self, board_id: str) -> Dict[str, Any]:
        """
        Get a specific board.
        
        Args:
            board_id (str): Board ID
        
        Returns:
            Dict[str, Any]: Board details
        """
        # Boards are part of the Jira Software REST API
        agile_api_base = f"{self.base_url}/rest/agile/1.0"
        url = f"{agile_api_base}/board/{board_id}"
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting board {board_id}: {str(e)}")
            raise
    
    def get_board_issues(
        self, 
        board_id: str,
        start_at: int = 0,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Get issues for a board.
        
        Args:
            board_id (str): Board ID
            start_at (int): Index of the first result
            max_results (int): Maximum number of results to return
        
        Returns:
            Dict[str, Any]: Board issues
        """
        # Boards are part of the Jira Software REST API
        agile_api_base = f"{self.base_url}/rest/agile/1.0"
        url = f"{agile_api_base}/board/{board_id}/issue"
        
        params = {
            "startAt": start_at,
            "maxResults": max_results
        }
        
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting board issues for board {board_id}: {str(e)}")
            raise
    
    # Sprints (Agile)
    
    def get_sprints(
        self, 
        board_id: str,
        state: Optional[str] = None,
        start_at: int = 0,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Get all sprints for a board.
        
        Args:
            board_id (str): Board ID
            state (Optional[str]): Filter by sprint state (future, active, closed)
            start_at (int): Index of the first result
            max_results (int): Maximum number of results to return
        
        Returns:
            Dict[str, Any]: Sprints data
        """
        # Sprints are part of the Jira Software REST API
        agile_api_base = f"{self.base_url}/rest/agile/1.0"
        url = f"{agile_api_base}/board/{board_id}/sprint"
        
        params = {
            "startAt": start_at,
            "maxResults": max_results
        }
        
        if state:
            params["state"] = state
        
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting sprints for board {board_id}: {str(e)}")
            raise
    
    def get_sprint(self, sprint_id: str) -> Dict[str, Any]:
        """
        Get a specific sprint.
        
        Args:
            sprint_id (str): Sprint ID
        
        Returns:
            Dict[str, Any]: Sprint details
        """
        # Sprints are part of the Jira Software REST API
        agile_api_base = f"{self.base_url}/rest/agile/1.0"
        url = f"{agile_api_base}/sprint/{sprint_id}"
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting sprint {sprint_id}: {str(e)}")
            raise
    
    def get_sprint_issues(
        self,
        sprint_id: str,
        start_at: int = 0,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Get issues for a sprint.
        
        Args:
            sprint_id (str): Sprint ID
            start_at (int): Index of the first result
            max_results (int): Maximum number of results to return
        
        Returns:
            Dict[str, Any]: Sprint issues
        """
        # Sprints are part of the Jira Software REST API
        agile_api_base = f"{self.base_url}/rest/agile/1.0"
        url = f"{agile_api_base}/sprint/{sprint_id}/issue"
        
        params = {
            "startAt": start_at,
            "maxResults": max_results
        }
        
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting sprint issues for sprint {sprint_id}: {str(e)}")
            raise
    
    # Custom fields
    
    def get_custom_fields(self) -> List[Dict[str, Any]]:
        """
        Get all custom fields.
        
        Returns:
            List[Dict[str, Any]]: List of custom fields
        """
        return self._make_request("get", "field")
    
    # Dashboards
    
    def get_dashboards(
        self,
        start_at: int = 0,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Get all dashboards.
        
        Args:
            start_at (int): Index of the first result
            max_results (int): Maximum number of results to return
        
        Returns:
            Dict[str, Any]: Dashboards data
        """
        params = {
            "startAt": start_at,
            "maxResults": max_results
        }
        return self._make_request("get", "dashboard", params=params)
    
    def get_dashboard(self, dashboard_id: str) -> Dict[str, Any]:
        """
        Get a specific dashboard.
        
        Args:
            dashboard_id (str): Dashboard ID
        
        Returns:
            Dict[str, Any]: Dashboard details
        """
        return self._make_request("get", f"dashboard/{dashboard_id}")
    
    # Filters
    
    def get_filters(self) -> List[Dict[str, Any]]:
        """
        Get all filters.
        
        Returns:
            List[Dict[str, Any]]: List of filters
        """
        return self._make_request("get", "filter")
    
    def get_filter(self, filter_id: str) -> Dict[str, Any]:
        """
        Get a specific filter.
        
        Args:
            filter_id (str): Filter ID
        
        Returns:
            Dict[str, Any]: Filter details
        """
        return self._make_request("get", f"filter/{filter_id}")
    
    # Utility functions
    
    def export_issues_to_json(
        self, 
        jql: str, 
        filename: Optional[str] = None,
        fields: Optional[List[str]] = None
    ) -> str:
        """
        Export issues matching a JQL query to a JSON file.
        
        Args:
            jql (str): JQL query string
            filename (Optional[str]): Output filename (default: jira_export_{timestamp}.json)
            fields (Optional[List[str]]): List of fields to include
        
        Returns:
            str: Path to the exported file
        """
        issues = self.get_all_issues(jql=jql, fields=fields)
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"jira_export_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(issues, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Exported {len(issues)} issues to {filename}")
        return filename
    
    def get_issue_history(self, issue_id_or_key: str) -> List[Dict[str, Any]]:
        """
        Get the complete history of an issue including all updates and transitions.
        
        Args:
            issue_id_or_key (str): Issue ID or key
        
        Returns:
            List[Dict[str, Any]]: Issue history
        """
        issue = self.get_issue(issue_id_or_key, expand=["changelog"])
        return issue.get("changelog", {}).get("histories", [])

    def get_project_roles(self, project_id_or_key: str) -> Dict[str, str]:
        """
        Get all roles for a project.
        
        Args:
            project_id_or_key (str): Project ID or key
        
        Returns:
            Dict[str, str]: Dictionary mapping role names to role URLs
            
        Raises:
            requests.exceptions.HTTPError: If the request fails (e.g., unauthorized access)
        """
        logger.info(f"Getting roles for project {project_id_or_key}")
        try:
            return self._make_request("get", f"project/{project_id_or_key}/role")
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to get roles for project {project_id_or_key}: {str(e)}")
            raise
    
    def get_project_role(self, project_id_or_key: str, role_id: str) -> Dict[str, Any]:
        """
        Get details of a specific role for a project.
        
        Args:
            project_id_or_key (str): Project ID or key
            role_id (str): Role ID
        
        Returns:
            Dict[str, Any]: Role details including actors
            
        Raises:
            requests.exceptions.HTTPError: If the request fails (e.g., unauthorized access)
        """
        logger.info(f"Getting role {role_id} for project {project_id_or_key}")
        try:
            return self._make_request("get", f"project/{project_id_or_key}/role/{role_id}")
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to get role {role_id} for project {project_id_or_key}: {str(e)}")
            raise
    
    def add_user_to_project_role(
        self,
        project_id_or_key: str,
        role_id: str,
        account_id: str
    ) -> Dict[str, Any]:
        """
        Add a user to a project role.
        
        Args:
            project_id_or_key (str): Project ID or key
            role_id (str): Role ID
            account_id (str): User account ID
        
        Returns:
            Dict[str, Any]: Updated role details
            
        Raises:
            requests.exceptions.HTTPError: If the request fails (e.g., unauthorized access)
        """
        logger.info(f"Adding user {account_id} to role {role_id} for project {project_id_or_key}")
        data = {
            "user": [
                {
                    "accountId": account_id
                }
            ]
        }
        try:
            return self._make_request("post", f"project/{project_id_or_key}/role/{role_id}", data=data)
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to add user {account_id} to role {role_id} for project {project_id_or_key}: {str(e)}")
            raise
    
    def can_access_project_roles(self, project_id_or_key: str) -> bool:
        """
        Check if the authenticated user can access project roles.
        
        Args:
            project_id_or_key (str): Project ID or key
        
        Returns:
            bool: True if the user can access project roles, False otherwise
        """
        logger.info(f"Checking if user can access roles for project {project_id_or_key}")
        try:
            self.get_project_roles(project_id_or_key)
            return True
        except requests.exceptions.HTTPError as e:
            if e.response.status_code in [401, 403, 404]:
                logger.warning(f"User does not have access to roles for project {project_id_or_key}")
                return False
            else:
                # For other errors, re-raise the exception
                raise