"""
Vector Search Adapter for Jira Natural Language Search

This module provides an adapter for searching Jira ticket titles and descriptions
in Pinecone vector database.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from app.utils.pinecone.pinecone_service import PineconeService

logger = logging.getLogger(__name__)

class VectorSearchAdapter:
    """
    Adapter for searching Jira ticket titles and descriptions in Pinecone.
    
    This class handles the conversion of structured query plans into
    Pinecone vector search queries and processes the results.
    """
    
    def __init__(self, pinecone_service: Optional[PineconeService] = None):
        """
        Initialize the vector search adapter.
        
        Args:
            pinecone_service: Optional PineconeService instance
        """
        self.pinecone_service = pinecone_service or PineconeService()
    
    def search(self, query_plan: Dict[str, Any], max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Execute a vector search based on the query plan.
        
        Args:
            query_plan: The query plan generated by QueryPlanner
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Extract query parameters
            query_text = query_plan.get("query_text", "")
            organisation_id = query_plan.get("organisation_id")
            filter_dict = query_plan.get("filter", {})
            
            if not query_text or not organisation_id:
                logger.error("Missing required parameters for vector search")
                return []
            
            # Ensure organisation_id is in the filter
            if "organisation_id" not in filter_dict:
                filter_dict["organisation_id"] = organisation_id
            
            # Get file IDs for the organization (this would be adapted for Jira)
            # In a real implementation, this would be replaced with a method to get
            # the appropriate Jira ticket IDs based on access control
            file_ids = self._get_accessible_jira_ids(organisation_id, query_plan)
            
            if not file_ids:
                logger.warning(f"No accessible Jira tickets found for organisation {organisation_id}")
                return []
            
            # Execute vector search
            success, message, vector_results = self.pinecone_service.vector_search_only(
                query_text=query_text,
                file_ids=file_ids,
                top_k=max_results
            )
            
            if not success or not vector_results:
                logger.warning(f"Vector search failed or returned no results: {message}")
                return []
            
            # Process and enhance results
            processed_results = self._process_vector_results(vector_results, query_plan)
            
            logger.info(f"Vector search found {len(processed_results)} results")
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in vector search: {str(e)}")
            return []
    
    def _get_accessible_jira_ids(self, organisation_id: str, query_plan: Dict[str, Any]) -> List[str]:
        """
        Get accessible Jira ticket IDs for the organization.
        
        In a real implementation, this would query the appropriate service to get
        the IDs of Jira tickets that the user has access to.
        
        Args:
            organisation_id: Organization ID
            query_plan: The query plan with additional filters
            
        Returns:
            List of accessible Jira ticket IDs
        """
        try:
            # This is a placeholder implementation
            # In a real system, this would query Neo4j or another service to get
            # the IDs of Jira tickets that match the filter criteria
            
            # Extract filter criteria
            project_key = query_plan.get("project_key")
            issue_key = query_plan.get("issue_key")
            
            # Build Neo4j query to get matching ticket IDs
            from app.services.neo4j_service import execute_read_query
            
            query_params = {"organisation_id": organisation_id}
            
            if project_key:
                query_params["project_key"] = project_key
            
            if issue_key:
                query_params["issue_key"] = issue_key
            
            # Query to get all issue IDs that match the criteria
            cypher_query = """
            MATCH (i:Issue)
            WHERE i.organisation_id = $organisation_id
            """
            
            if project_key:
                cypher_query += " AND i.project_key = $project_key"
            
            if issue_key:
                cypher_query += " AND i.key = $issue_key"
            
            cypher_query += """
            RETURN i.id as id, i.key as key
            LIMIT 1000
            """
            
            results = execute_read_query(cypher_query, query_params)
            
            # Extract IDs from results
            issue_ids = [result.get("id") for result in results if result.get("id")]
            
            # If no specific IDs found but we have a project, get all issues for that project
            if not issue_ids and project_key:
                project_query = """
                MATCH (p:Project {key: $project_key, org_id: $organisation_id})-[:HAS_ISSUE]->(i:Issue)
                RETURN i.id as id, i.key as key
                LIMIT 1000
                """
                project_results = execute_read_query(project_query, query_params)
                issue_ids = [result.get("id") for result in project_results if result.get("id")]
            
            # If still no IDs found, get a sample of recent issues
            if not issue_ids:
                fallback_query = """
                MATCH (i:Issue)
                WHERE i.organisation_id = $organisation_id
                RETURN i.id as id, i.key as key
                ORDER BY i.created_at DESC
                LIMIT 100
                """
                fallback_results = execute_read_query(fallback_query, {"organisation_id": organisation_id})
                issue_ids = [result.get("id") for result in fallback_results if result.get("id")]
            
            return issue_ids
            
        except Exception as e:
            logger.error(f"Error getting accessible Jira IDs: {str(e)}")
            return []
    
    def _process_vector_results(self, vector_results: List[Dict[str, Any]], 
                              query_plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process and enhance vector search results.
        
        Args:
            vector_results: Raw vector search results from Pinecone
            query_plan: The original query plan
            
        Returns:
            Processed and enhanced results
        """
        processed_results = []
        
        for result in vector_results:
            # Extract basic information
            file_id = result.get("file_id", "")
            score = result.get("score", 0.0)
            vector_id = result.get("vector_id", "")
            chunk_text = result.get("chunk_text", "")
            
            # Get additional Jira ticket information from Neo4j
            ticket_info = self._get_ticket_info(file_id)
            
            # Combine information
            processed_result = {
                "id": file_id,
                "key": ticket_info.get("key", ""),
                "title": ticket_info.get("title", ""),
                "description": chunk_text,  # Use the chunk text as description
                "project_key": ticket_info.get("project_key", ""),
                "status": ticket_info.get("status", ""),
                "priority": ticket_info.get("priority", ""),
                "assignee": ticket_info.get("assignee", ""),
                "reporter": ticket_info.get("reporter", ""),
                "created_at": ticket_info.get("created_at", ""),
                "updated_at": ticket_info.get("updated_at", ""),
                "score": score,
                "vector_id": vector_id,
                "search_type": "semantic"
            }
            
            processed_results.append(processed_result)
        
        return processed_results
    
    def _get_ticket_info(self, ticket_id: str) -> Dict[str, Any]:
        """
        Get additional information about a Jira ticket from Neo4j.
        
        Args:
            ticket_id: The ID of the Jira ticket
            
        Returns:
            Dictionary with ticket information
        """
        try:
            from app.services.neo4j_service import execute_read_query
            
            # Query Neo4j for ticket information
            cypher_query = """
            MATCH (i:Issue {id: $ticket_id})
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            RETURN i.key as key, 
                   i.title as title, 
                   i.project_key as project_key,
                   i.status as status,
                   i.priority as priority,
                   assignee.name as assignee_name,
                   reporter.name as reporter_name,
                   i.created_at as created_at,
                   i.updated_at as updated_at
            """
            
            results = execute_read_query(cypher_query, {"ticket_id": ticket_id})
            
            if results:
                ticket_info = {
                    "key": results[0].get("key", ""),
                    "title": results[0].get("title", ""),
                    "project_key": results[0].get("project_key", ""),
                    "status": results[0].get("status", ""),
                    "priority": results[0].get("priority", ""),
                    "assignee": results[0].get("assignee_name", ""),
                    "reporter": results[0].get("reporter_name", ""),
                    "created_at": results[0].get("created_at", ""),
                    "updated_at": results[0].get("updated_at", "")
                }
                return ticket_info
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Error getting ticket info: {str(e)}")
            return {}