"""
SQL Adapter for Jira Natural Language Search

This module provides an adapter for searching Jira comments in PostgreSQL database.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import text, or_, and_, desc

from app.modules.connectors.handlers.jira.repository.jira_comments_repository import JiraCommentsRepository
from app.modules.connectors.handlers.jira.models.jira_comments import JiraComment
from app.db.postgres import db_session

logger = logging.getLogger(__name__)

class SQLAdapter:
    """
    Adapter for searching Jira comments in PostgreSQL.
    
    This class handles the conversion of structured query plans into
    SQL queries for searching Jira comments in PostgreSQL.
    """
    
    def __init__(self, comments_repository: Optional[JiraCommentsRepository] = None):
        """
        Initialize the SQL adapter.
        
        Args:
            comments_repository: Optional JiraCommentsRepository instance
        """
        self.comments_repository = comments_repository or JiraCommentsRepository()
    
    def search(self, query_plan: Dict[str, Any], max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Execute a SQL search based on the query plan.
        
        Args:
            query_plan: The query plan generated by QueryPlanner
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Extract query parameters
            query_text = query_plan.get("query_text", "")
            organisation_id = query_plan.get("organisation_id")
            project_key = query_plan.get("project_key")
            issue_key = query_plan.get("issue_key")
            user_email = query_plan.get("user_email")
            start_date = query_plan.get("start_date")
            end_date = query_plan.get("end_date")
            
            if not organisation_id:
                logger.error("Missing required organisation_id parameter for SQL search")
                return []
            
            # Build SQL query
            query = db_session.query(JiraComment)
            
            # Add organization filter (always required)
            query = query.filter(JiraComment.org_id == organisation_id)
            
            # Add project filter if provided
            if project_key:
                query = query.filter(JiraComment.project_key == project_key)
            
            # Add issue filter if provided
            if issue_key:
                query = query.filter(JiraComment.issue_key == issue_key)
            
            # Add user filter if provided
            if user_email:
                query = query.filter(JiraComment.user_email == user_email)
            
            # Add date range filters if provided
            if start_date:
                try:
                    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(JiraComment.comment_date >= start_datetime)
                except ValueError:
                    logger.warning(f"Invalid start_date format: {start_date}")
            
            if end_date:
                try:
                    end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                    # Add 1 day to include the end date
                    end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                    query = query.filter(JiraComment.comment_date <= end_datetime)
                except ValueError:
                    logger.warning(f"Invalid end_date format: {end_date}")
            
            # Add full-text search if query text is provided
            if query_text:
                # Clean and prepare search terms
                search_terms = self._prepare_search_terms(query_text)
                
                # Build OR conditions for each search term
                text_conditions = []
                for term in search_terms:
                    text_conditions.append(JiraComment.comment.ilike(f"%{term}%"))
                
                if text_conditions:
                    query = query.filter(or_(*text_conditions))
            
            # Order by most recent first
            query = query.order_by(desc(JiraComment.comment_date))
            
            # Limit results
            query = query.limit(max_results)
            
            # Execute query
            comments = query.all()
            
            # Process results
            processed_results = self._process_sql_results(comments, query_plan)
            
            logger.info(f"SQL search found {len(processed_results)} results")
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in SQL search: {str(e)}")
            return []
    
    def _prepare_search_terms(self, query_text: str) -> List[str]:
        """
        Prepare search terms from query text.
        
        Args:
            query_text: The query text
            
        Returns:
            List of search terms
        """
        # Convert to lowercase
        query_lower = query_text.lower()
        
        # Remove common filler words
        query_lower = re.sub(r'\b(find|show|get|give me|display|comments|comment|containing|with|about|in|the|a|an|and|or|but|for|on|at|to|from|by|of)\b', ' ', query_lower)
        
        # Split into terms and filter out short terms
        terms = [term.strip() for term in query_lower.split() if len(term.strip()) > 2]
        
        # Remove duplicates while preserving order
        unique_terms = []
        for term in terms:
            if term not in unique_terms:
                unique_terms.append(term)
        
        return unique_terms
    
    def _process_sql_results(self, comments: List[JiraComment], 
                           query_plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process SQL search results.
        
        Args:
            comments: List of JiraComment objects
            query_plan: The original query plan
            
        Returns:
            Processed results
        """
        processed_results = []
        
        for comment in comments:
            # Get additional issue information from Neo4j
            issue_info = self._get_issue_info(comment.issue_key)
            
            # Create result object
            result = {
                "id": comment.uuid,
                "issue_key": comment.issue_key,
                "project_key": comment.project_key,
                "comment": comment.comment,
                "comment_date": comment.comment_date.isoformat() if comment.comment_date else None,
                "user_email": comment.user_email,
                "user_id": comment.user_id,
                "created_at": comment.created_at.isoformat() if comment.created_at else None,
                "issue_title": issue_info.get("title", ""),
                "issue_status": issue_info.get("status", ""),
                "issue_priority": issue_info.get("priority", ""),
                "assignee": issue_info.get("assignee", ""),
                "reporter": issue_info.get("reporter", ""),
                "search_type": "comments"
            }
            
            processed_results.append(result)
        
        return processed_results
    
    def _get_issue_info(self, issue_key: str) -> Dict[str, Any]:
        """
        Get additional information about a Jira issue from Neo4j.
        
        Args:
            issue_key: The key of the Jira issue
            
        Returns:
            Dictionary with issue information
        """
        try:
            from app.services.neo4j_service import execute_read_query
            
            # Query Neo4j for issue information
            cypher_query = """
            MATCH (i:Issue {key: $issue_key})
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            RETURN i.title as title, 
                   i.status as status,
                   i.priority as priority,
                   assignee.name as assignee_name,
                   reporter.name as reporter_name
            """
            
            results = execute_read_query(cypher_query, {"issue_key": issue_key})
            
            if results:
                issue_info = {
                    "title": results[0].get("title", ""),
                    "status": results[0].get("status", ""),
                    "priority": results[0].get("priority", ""),
                    "assignee": results[0].get("assignee_name", ""),
                    "reporter": results[0].get("reporter_name", "")
                }
                return issue_info
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Error getting issue info: {str(e)}")
            return {}
    
    def search_by_issue(self, issue_key: str, organisation_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search comments for a specific issue.
        
        Args:
            issue_key: The key of the Jira issue
            organisation_id: Organization ID
            max_results: Maximum number of results to return
            
        Returns:
            List of comments for the issue
        """
        try:
            # Use the repository method to get comments by issue
            comments = self.comments_repository.get_comments_by_issue(
                org_id=organisation_id,
                issue_key=issue_key
            )
            
            # Limit results
            comments = comments[:max_results]
            
            # Process results
            query_plan = {
                "issue_key": issue_key,
                "organisation_id": organisation_id
            }
            
            return self._process_sql_results(comments, query_plan)
            
        except Exception as e:
            logger.error(f"Error in search_by_issue: {str(e)}")
            return []
    
    def search_by_project(self, project_key: str, organisation_id: str, 
                        max_results: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Search comments for a specific project.
        
        Args:
            project_key: The key of the Jira project
            organisation_id: Organization ID
            max_results: Maximum number of results to return
            offset: Number of results to skip
            
        Returns:
            List of comments for the project
        """
        try:
            # Use the repository method to get comments by project
            comments = self.comments_repository.get_comments_by_project(
                org_id=organisation_id,
                project_key=project_key,
                limit=max_results,
                offset=offset
            )
            
            # Process results
            query_plan = {
                "project_key": project_key,
                "organisation_id": organisation_id
            }
            
            return self._process_sql_results(comments, query_plan)
            
        except Exception as e:
            logger.error(f"Error in search_by_project: {str(e)}")
            return []
    
    def search_by_user(self, user_id: str, organisation_id: str, 
                     max_results: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Search comments by a specific user.
        
        Args:
            user_id: The ID of the Jira user
            organisation_id: Organization ID
            max_results: Maximum number of results to return
            offset: Number of results to skip
            
        Returns:
            List of comments by the user
        """
        try:
            # Use the repository method to get comments by user
            comments = self.comments_repository.get_comments_by_user(
                org_id=organisation_id,
                user_id=user_id,
                limit=max_results,
                offset=offset
            )
            
            # Process results
            query_plan = {
                "user_id": user_id,
                "organisation_id": organisation_id
            }
            
            return self._process_sql_results(comments, query_plan)
            
        except Exception as e:
            logger.error(f"Error in search_by_user: {str(e)}")
            return []