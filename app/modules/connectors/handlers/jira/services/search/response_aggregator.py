"""
Response Aggregator for Jira Natural Language Search

This module provides functionality to combine and rank search results from
different databases (Pinecone, PostgreSQL, Neo4j) into a coherent response.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from app.utils.llm.client import LLMClient

logger = logging.getLogger(__name__)

class ResponseAggregator:
    """
    Aggregates and ranks search results from different databases.
    
    This class combines results from Pinecone (semantic search), PostgreSQL (comments),
    and Neo4j (structured data) into a coherent response, with optional LLM enhancement.
    """
    
    def __init__(self, use_llm: bool = True):
        """
        Initialize the response aggregator.
        
        Args:
            use_llm: Whether to use LLM for response enhancement (if available)
        """
        self.use_llm = use_llm
        self.llm_client = LLMClient() if use_llm else None
    
    def aggregate(self, query_text: str, results: Dict[str, List[Dict[str, Any]]], 
                max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Aggregate results from different databases.
        
        Args:
            query_text: The original query text
            results: Dictionary mapping intent types to result lists
            max_results: Maximum number of results to return
            
        Returns:
            Aggregated and ranked results
        """
        try:
            # Collect all results
            all_results = []
            
            # Track seen issue keys to avoid duplicates
            seen_issue_keys = set()
            
            # Process semantic results first (highest priority)
            semantic_results = results.get("semantic", [])
            for result in semantic_results:
                issue_key = result.get("key")
                if issue_key and issue_key not in seen_issue_keys:
                    seen_issue_keys.add(issue_key)
                    all_results.append(self._enhance_result(result, results))
            
            # Process metadata results next
            metadata_results = results.get("metadata", [])
            for result in metadata_results:
                issue_key = result.get("key")
                if issue_key and issue_key not in seen_issue_keys:
                    seen_issue_keys.add(issue_key)
                    all_results.append(self._enhance_result(result, results))
            
            # Process comments results last
            comments_results = results.get("comments", [])
            for result in comments_results:
                issue_key = result.get("issue_key")
                if issue_key and issue_key not in seen_issue_keys:
                    # For comment results, we need to get the issue details
                    issue_result = self._find_issue_by_key(issue_key, results)
                    if issue_result:
                        # Add comment to issue result
                        if "comments" not in issue_result:
                            issue_result["comments"] = []
                        issue_result["comments"].append(result)
                        
                        # Only add if not already in results
                        if issue_key not in seen_issue_keys:
                            seen_issue_keys.add(issue_key)
                            all_results.append(issue_result)
                    else:
                        # If issue not found, add comment result directly
                        seen_issue_keys.add(issue_key)
                        all_results.append(result)
            
            # Process fallback results if no other results
            if not all_results and "fallback" in results:
                fallback_results = results.get("fallback", [])
                for result in fallback_results:
                    issue_key = result.get("key")
                    if issue_key and issue_key not in seen_issue_keys:
                        seen_issue_keys.add(issue_key)
                        all_results.append(self._enhance_result(result, results))
            
            # Rank results
            ranked_results = self._rank_results(query_text, all_results)
            
            # Limit results
            limited_results = ranked_results[:max_results]
            
            # Enhance with LLM if enabled
            if self.use_llm and self.llm_client and self.llm_client.is_available():
                enhanced_results = self._enhance_with_llm(query_text, limited_results)
                if enhanced_results:
                    return enhanced_results
            
            return limited_results
            
        except Exception as e:
            logger.error(f"Error in response aggregation: {str(e)}")
            # Return whatever results we have
            flattened_results = []
            for result_list in results.values():
                flattened_results.extend(result_list)
            return flattened_results[:max_results]
    
    def _enhance_result(self, result: Dict[str, Any], all_results: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Enhance a result with information from other sources.
        
        Args:
            result: The result to enhance
            all_results: All results from different sources
            
        Returns:
            Enhanced result
        """
        # Get issue key
        issue_key = result.get("key") or result.get("issue_key")
        if not issue_key:
            return result
        
        # Create a copy of the result to avoid modifying the original
        enhanced_result = result.copy()
        
        # Add comments if available
        if "comments" not in enhanced_result:
            comments = self._find_comments_for_issue(issue_key, all_results.get("comments", []))
            if comments:
                enhanced_result["comments"] = comments
        
        # Add metadata if available and not already present
        if enhanced_result.get("search_type") != "metadata":
            metadata = self._find_metadata_for_issue(issue_key, all_results.get("metadata", []))
            if metadata:
                # Add missing metadata fields
                for key, value in metadata.items():
                    if key not in enhanced_result or not enhanced_result[key]:
                        enhanced_result[key] = value
        
        return enhanced_result
    
    def _find_issue_by_key(self, issue_key: str, results: Dict[str, List[Dict[str, Any]]]) -> Optional[Dict[str, Any]]:
        """
        Find an issue by key in the results.
        
        Args:
            issue_key: The issue key to find
            results: All results from different sources
            
        Returns:
            Issue result or None if not found
        """
        # Check semantic results first
        for result in results.get("semantic", []):
            if result.get("key") == issue_key:
                return result
        
        # Check metadata results next
        for result in results.get("metadata", []):
            if result.get("key") == issue_key:
                return result
        
        # Check fallback results last
        for result in results.get("fallback", []):
            if result.get("key") == issue_key:
                return result
        
        return None
    
    def _find_comments_for_issue(self, issue_key: str, comments_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Find comments for an issue.
        
        Args:
            issue_key: The issue key to find comments for
            comments_results: All comment results
            
        Returns:
            List of comments for the issue
        """
        return [comment for comment in comments_results if comment.get("issue_key") == issue_key]
    
    def _find_metadata_for_issue(self, issue_key: str, metadata_results: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Find metadata for an issue.
        
        Args:
            issue_key: The issue key to find metadata for
            metadata_results: All metadata results
            
        Returns:
            Metadata for the issue or None if not found
        """
        for result in metadata_results:
            if result.get("key") == issue_key:
                return result
        
        return None
    
    def _rank_results(self, query_text: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Rank results based on relevance to the query.
        
        Args:
            query_text: The original query text
            results: List of results to rank
            
        Returns:
            Ranked results
        """
        # Calculate relevance scores
        for result in results:
            relevance_score = 0.0
            
            # Base score from search type
            search_type = result.get("search_type", "")
            if search_type == "semantic":
                relevance_score += 0.5
            elif search_type == "metadata":
                relevance_score += 0.3
            elif search_type == "comments":
                relevance_score += 0.2
            
            # Boost for having comments
            if result.get("comments"):
                relevance_score += 0.2
            
            # Boost for having detailed metadata
            if result.get("assignee") or result.get("reporter") or result.get("sprint"):
                relevance_score += 0.1
            
            # Boost for matching query terms in title or description
            title = result.get("title", "").lower()
            description = result.get("description", "").lower()
            query_lower = query_text.lower()
            
            query_terms = [term for term in query_lower.split() if len(term) > 2]
            matching_terms = sum(1 for term in query_terms if term in title or term in description)
            term_boost = min(0.3, matching_terms * 0.1)
            relevance_score += term_boost
            
            # Boost for recency
            try:
                updated_at = result.get("updated_at")
                if updated_at:
                    if isinstance(updated_at, str):
                        updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    
                    # Calculate days since update
                    days_ago = (datetime.now() - updated_at).days
                    if days_ago < 7:
                        relevance_score += 0.2
                    elif days_ago < 30:
                        relevance_score += 0.1
            except Exception as e:
                logger.warning(f"Error calculating recency boost: {e}")
            
            # Store the calculated relevance score
            result["relevance_score"] = min(1.0, relevance_score)
        
        # Sort by relevance score (descending)
        return sorted(results, key=lambda x: x.get("relevance_score", 0.0), reverse=True)
    
    def _enhance_with_llm(self, query_text: str, results: List[Dict[str, Any]]) -> Optional[List[Dict[str, Any]]]:
        """
        Enhance results with LLM.
        
        Args:
            query_text: The original query text
            results: List of results to enhance
            
        Returns:
            Enhanced results or None if enhancement failed
        """
        try:
            # Prepare prompt for LLM
            prompt = f"""
            Analyze the following search results for the query: "{query_text}"
            
            Search Results:
            {self._format_results_for_llm(results)}
            
            Please provide a brief summary of each result, highlighting why it's relevant to the query.
            Focus on the most important information and how it addresses the user's question.
            
            Respond with a JSON array where each object contains:
            1. "id": The original result ID
            2. "key": The issue key
            3. "title": The issue title
            4. "summary": Your generated summary
            5. "relevance_explanation": Why this result is relevant to the query
            6. "relevance_score": A number between 0 and 1 indicating relevance
            
            Keep all original fields from the results intact.
            """
            
            # Call LLM
            response = self.llm_client.generate_text(prompt)
            
            # Parse response
            import json
            try:
                enhanced_data = json.loads(response)
                
                # Validate response format
                if not isinstance(enhanced_data, list):
                    logger.warning("LLM response is not a list")
                    return None
                
                # Merge LLM enhancements with original results
                enhanced_results = []
                for i, result in enumerate(results):
                    if i < len(enhanced_data):
                        # Get LLM enhancement
                        enhancement = enhanced_data[i]
                        
                        # Create enhanced result
                        enhanced_result = result.copy()
                        
                        # Add LLM-generated fields
                        enhanced_result["summary"] = enhancement.get("summary", "")
                        enhanced_result["relevance_explanation"] = enhancement.get("relevance_explanation", "")
                        
                        # Update relevance score if provided
                        if "relevance_score" in enhancement:
                            enhanced_result["relevance_score"] = enhancement["relevance_score"]
                        
                        enhanced_results.append(enhanced_result)
                    else:
                        # If LLM didn't provide enhancement for this result, use original
                        enhanced_results.append(result)
                
                return enhanced_results
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse LLM response: {e}")
                return None
                
        except Exception as e:
            logger.error(f"Error in LLM enhancement: {e}")
            return None
    
    def _format_results_for_llm(self, results: List[Dict[str, Any]]) -> str:
        """
        Format results for LLM prompt.
        
        Args:
            results: List of results to format
            
        Returns:
            Formatted results string
        """
        formatted_results = []
        
        for i, result in enumerate(results):
            # Format basic information
            formatted_result = f"Result {i+1}:\n"
            formatted_result += f"- Issue Key: {result.get('key', '')}\n"
            formatted_result += f"- Title: {result.get('title', '')}\n"
            formatted_result += f"- Status: {result.get('status', '')}\n"
            formatted_result += f"- Priority: {result.get('priority', '')}\n"
            
            # Format description (truncate if too long)
            description = result.get('description', '')
            if description:
                if len(description) > 300:
                    description = description[:300] + "..."
                formatted_result += f"- Description: {description}\n"
            
            # Format assignee and reporter
            assignee = result.get('assignee', {})
            if assignee and isinstance(assignee, dict):
                formatted_result += f"- Assignee: {assignee.get('name', '')}\n"
            
            reporter = result.get('reporter', {})
            if reporter and isinstance(reporter, dict):
                formatted_result += f"- Reporter: {reporter.get('name', '')}\n"
            
            # Format comments (truncate if too many)
            comments = result.get('comments', [])
            if comments:
                formatted_result += f"- Comments ({len(comments)}):\n"
                for j, comment in enumerate(comments[:3]):  # Show at most 3 comments
                    comment_text = comment.get('comment', '')
                    if comment_text:
                        if len(comment_text) > 200:
                            comment_text = comment_text[:200] + "..."
                        formatted_result += f"  * {comment_text}\n"
                
                if len(comments) > 3:
                    formatted_result += f"  * ... and {len(comments) - 3} more comments\n"
            
            formatted_results.append(formatted_result)
        
        return "\n".join(formatted_results)