"""
Intent Parser for Jira Natural Language Search

This module analyzes natural language queries to determine the appropriate
search intent(s) and which database(s) to query.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from app.utils.llm.client import LLMClient

logger = logging.getLogger(__name__)

class SearchIntent:
    """Enum-like class for search intent types"""
    SEMANTIC = "semantic"  # Search for ticket summaries or descriptions (Pinecone)
    COMMENTS = "comments"  # Search for comments or discussion context (PostgreSQL)
    METADATA = "metadata"  # Search for structured metadata (Neo4j)
    HYBRID = "hybrid"      # Combination of multiple intents

class IntentParser:
    """
    Analyzes natural language queries to determine search intent.
    
    This class uses pattern matching and optional LLM assistance to determine
    which database(s) should be queried based on the user's query.
    """
    
    def __init__(self, use_llm: bool = True):
        """
        Initialize the intent parser.
        
        Args:
            use_llm: Whether to use LLM for intent detection (if available)
        """
        self.use_llm = use_llm
        self.llm_client = LLMClient() if use_llm else None
        
        # Initialize pattern dictionaries for rule-based intent detection
        self._init_patterns()
    
    def _init_patterns(self):
        """Initialize regex patterns for intent detection."""
        # Patterns for semantic search (Pinecone)
        self.semantic_patterns = [
            r'\b(about|describe|explain|summary|description|overview)\b',
            r'\b(what is|what are|tell me about)\b',
            r'\b(find|search for|look for)\b.*\b(ticket|issue|story|epic)\b',
            r'\b(similar to|like|related to)\b',
            r'\b(content|text|title)\b'
        ]
        
        # Patterns for comments search (PostgreSQL)
        self.comments_patterns = [
            r'\b(comment|discussion|conversation|thread|reply|feedback)\b',
            r'\b(said|mentioned|wrote|posted|commented)\b',
            r'\b(who said|who mentioned|who commented)\b',
            r'\b(in the comments|in comments|comment section)\b',
            r'\b(latest comment|recent comment|last comment)\b'
        ]
        
        # Patterns for metadata search (Neo4j)
        self.metadata_patterns = [
            r'\b(assigned to|assignee|reporter|created by|author)\b',
            r'\b(priority|status|due date|deadline|sprint)\b',
            r'\b(blocked by|blocks|depends on|related to)\b',
            r'\b(project|board|epic|parent)\b',
            r'\b(created|updated|resolved|closed)\b.*\b(on|before|after|between)\b',
            r'\b(high priority|urgent|critical|blocker)\b',
            r'\b(in progress|done|to do|backlog)\b',
            r'\b(story points|estimate|time tracking)\b',
            r'\b(label|component|version|fix version|affected version)\b',
            r'\b(structure|hierarchy|relationship|link)\b'
        ]
        
        # Patterns for hybrid search
        self.hybrid_patterns = [
            r'\b(and|or)\b.*\b(comment|status|priority|assigned|description)\b',
            r'\b(both|all)\b',
            r'\b(comprehensive|complete|detailed|full)\b.*\b(search|results|information)\b'
        ]
    
    def parse_intent(self, query_text: str) -> Tuple[Set[str], float]:
        """
        Parse the intent from a natural language query.
        
        Args:
            query_text: The natural language query
            
        Returns:
            Tuple containing:
            - Set of intent types (semantic, comments, metadata, hybrid)
            - Confidence score (0.0 to 1.0)
        """
        # Try LLM-based intent detection first if enabled
        if self.use_llm and self.llm_client and self.llm_client.is_available():
            llm_intents, llm_confidence = self._llm_intent_detection(query_text)
            if llm_confidence > 0.7:
                logger.info(f"Using LLM-detected intents: {llm_intents} with confidence {llm_confidence}")
                return llm_intents, llm_confidence
        
        # Fall back to rule-based intent detection
        return self._rule_based_intent_detection(query_text)
    
    def _rule_based_intent_detection(self, query_text: str) -> Tuple[Set[str], float]:
        """
        Detect intent using rule-based pattern matching.
        
        Args:
            query_text: The natural language query
            
        Returns:
            Tuple containing:
            - Set of intent types
            - Confidence score
        """
        query_lower = query_text.lower()
        intents = set()
        confidence_scores = {}
        
        # Check for semantic search intent
        semantic_matches = sum(1 for pattern in self.semantic_patterns if re.search(pattern, query_lower))
        if semantic_matches > 0:
            intents.add(SearchIntent.SEMANTIC)
            confidence_scores[SearchIntent.SEMANTIC] = min(0.9, semantic_matches / len(self.semantic_patterns) + 0.5)
        
        # Check for comments search intent
        comments_matches = sum(1 for pattern in self.comments_patterns if re.search(pattern, query_lower))
        if comments_matches > 0:
            intents.add(SearchIntent.COMMENTS)
            confidence_scores[SearchIntent.COMMENTS] = min(0.9, comments_matches / len(self.comments_patterns) + 0.5)
        
        # Check for metadata search intent
        metadata_matches = sum(1 for pattern in self.metadata_patterns if re.search(pattern, query_lower))
        if metadata_matches > 0:
            intents.add(SearchIntent.METADATA)
            confidence_scores[SearchIntent.METADATA] = min(0.9, metadata_matches / len(self.metadata_patterns) + 0.5)
        
        # Check for hybrid search intent
        hybrid_matches = sum(1 for pattern in self.hybrid_patterns if re.search(pattern, query_lower))
        if hybrid_matches > 0 or len(intents) > 1:
            intents.add(SearchIntent.HYBRID)
            confidence_scores[SearchIntent.HYBRID] = min(0.9, hybrid_matches / len(self.hybrid_patterns) + 0.6)
        
        # If no intents detected, default to semantic search
        if not intents:
            intents.add(SearchIntent.SEMANTIC)
            confidence_scores[SearchIntent.SEMANTIC] = 0.5
        
        # Calculate overall confidence
        if len(confidence_scores) > 0:
            overall_confidence = sum(confidence_scores.values()) / len(confidence_scores)
        else:
            overall_confidence = 0.5
        
        return intents, overall_confidence
    
    def _llm_intent_detection(self, query_text: str) -> Tuple[Set[str], float]:
        """
        Detect intent using LLM.
        
        Args:
            query_text: The natural language query
            
        Returns:
            Tuple containing:
            - Set of intent types
            - Confidence score
        """
        try:
            # Prepare prompt for LLM
            prompt = f"""
            Analyze the following user query for a Jira search system and determine which database(s) should be queried.
            
            Query: "{query_text}"
            
            The system has three databases:
            1. Vector DB (Pinecone): Contains semantic embeddings of Jira ticket titles and descriptions
            2. SQL DB (PostgreSQL): Contains Jira comments and discussions
            3. Graph DB (Neo4j): Contains structured metadata like assignee, reporter, priority, status, etc.
            
            Respond with a JSON object containing:
            1. "intents": An array of database types to query (possible values: "semantic", "comments", "metadata", "hybrid")
            2. "confidence": A number between 0 and 1 indicating your confidence
            3. "reasoning": A brief explanation of your decision
            
            Example response:
            {{
                "intents": ["semantic", "metadata"],
                "confidence": 0.85,
                "reasoning": "The query asks about ticket content (semantic) and also mentions assignee (metadata)"
            }}
            """
            
            # Call LLM
            response = self.llm_client.generate_text(prompt)
            
            # Parse response
            import json
            try:
                result = json.loads(response)
                intents = set(result.get("intents", []))
                confidence = float(result.get("confidence", 0.5))
                
                # Validate intents
                valid_intents = {SearchIntent.SEMANTIC, SearchIntent.COMMENTS, SearchIntent.METADATA, SearchIntent.HYBRID}
                intents = {intent for intent in intents if intent in valid_intents}
                
                # If no valid intents, default to semantic
                if not intents:
                    intents = {SearchIntent.SEMANTIC}
                    confidence = 0.5
                
                return intents, confidence
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse LLM response: {e}")
                return self._rule_based_intent_detection(query_text)
                
        except Exception as e:
            logger.error(f"Error in LLM intent detection: {e}")
            return self._rule_based_intent_detection(query_text)