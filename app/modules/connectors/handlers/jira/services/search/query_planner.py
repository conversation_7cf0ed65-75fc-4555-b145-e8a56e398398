"""
Query Planner for Jira Natural Language Search

This module generates appropriate queries for each database based on the
detected intent from a natural language query.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from app.utils.llm.client import LLMClient

logger = logging.getLogger(__name__)

class SearchIntent:
    """Enum-like class for search intent types"""
    SEMANTIC = "semantic"  # Search for ticket summaries or descriptions (Pinecone)
    COMMENTS = "comments"  # Search for comments or discussion context (PostgreSQL)
    METADATA = "metadata"  # Search for structured metadata (Neo4j)
    HYBRID = "hybrid"      # Combination of multiple intents

class QueryPlanner:
    """
    Generates appropriate queries for each database based on detected intent.
    
    This class takes a natural language query and generates structured query plans
    for each relevant database (Pinecone, PostgreSQL, Neo4j).
    """
    
    def __init__(self, use_llm: bool = True):
        """
        Initialize the query planner.
        
        Args:
            use_llm: Whether to use LLM for query planning (if available)
        """
        self.use_llm = use_llm
        self.llm_client = LLMClient() if use_llm else None
        
        # Initialize extractors for different query components
        self._init_extractors()
    
    def _init_extractors(self):
        """Initialize regex patterns for extracting query components."""
        # Project key extractor (e.g., "PROJ-123", "PROJECT")
        self.project_key_pattern = r'\b([A-Z][A-Z0-9_]+)(?:-\d+)?\b'
        
        # Issue key extractor (e.g., "PROJ-123")
        self.issue_key_pattern = r'\b([A-Z][A-Z0-9_]+-\d+)\b'
        
        # User/assignee extractor
        self.user_patterns = [
            r'(?:assigned to|assignee|assigned by)\s+([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',  # Email
            r'(?:assigned to|assignee|assigned by)\s+([a-zA-Z]+\s+[a-zA-Z]+)',  # Full name
            r'(?:assigned to|assignee|assigned by)\s+([a-zA-Z]+)'  # First name
        ]
        
        # Status extractor
        self.status_patterns = [
            r'\b(open|closed|in progress|done|to do|backlog|resolved)\b',
            r'status\s+(?:is|=|:)\s+([a-zA-Z\s]+)'
        ]
        
        # Priority extractor
        self.priority_patterns = [
            r'\b(highest|high|medium|low|lowest|critical|blocker|major|minor|trivial)\b',
            r'priority\s+(?:is|=|:)\s+([a-zA-Z]+)'
        ]
        
        # Date range extractor
        self.date_patterns = [
            r'(?:created|updated|resolved|closed)\s+(?:on|before|after)\s+(\d{4}-\d{2}-\d{2})',
            r'(?:created|updated|resolved|closed)\s+(?:on|before|after)\s+(\d{1,2}/\d{1,2}/\d{2,4})',
            r'(?:in the last|within|in past)\s+(\d+)\s+(day|days|week|weeks|month|months|year|years)'
        ]
        
        # Sprint extractor
        self.sprint_patterns = [
            r'(?:in|during)\s+sprint\s+([a-zA-Z0-9\s]+)',
            r'sprint\s+([a-zA-Z0-9\s]+)'
        ]
    
    def generate_query_plans(self, query_text: str, intents: Set[str], 
                           organisation_id: str, user_id: Optional[str] = None,
                           project_key: Optional[str] = None, 
                           issue_key: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Generate query plans for each detected intent.
        
        Args:
            query_text: The natural language query
            intents: Set of detected intents
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Dictionary mapping intent types to query plans
        """
        query_plans = {}
        
        # Try LLM-based query planning first if enabled
        if self.use_llm and self.llm_client and self.llm_client.is_available():
            llm_plans = self._llm_query_planning(query_text, intents, organisation_id, user_id, project_key, issue_key)
            if llm_plans:
                logger.info(f"Using LLM-generated query plans for intents: {intents}")
                return llm_plans
        
        # Fall back to rule-based query planning
        for intent in intents:
            if intent == SearchIntent.SEMANTIC:
                query_plans[intent] = self._generate_semantic_query_plan(
                    query_text, organisation_id, user_id, project_key, issue_key
                )
            elif intent == SearchIntent.COMMENTS:
                query_plans[intent] = self._generate_comments_query_plan(
                    query_text, organisation_id, user_id, project_key, issue_key
                )
            elif intent == SearchIntent.METADATA:
                query_plans[intent] = self._generate_metadata_query_plan(
                    query_text, organisation_id, user_id, project_key, issue_key
                )
        
        return query_plans
    
    def generate_query_plan(self, query_text: str, intent: str, 
                          organisation_id: str, user_id: Optional[str] = None,
                          project_key: Optional[str] = None, 
                          issue_key: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a query plan for a specific intent.
        
        Args:
            query_text: The natural language query
            intent: The specific intent to generate a plan for
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Query plan dictionary
        """
        if intent == SearchIntent.SEMANTIC:
            return self._generate_semantic_query_plan(
                query_text, organisation_id, user_id, project_key, issue_key
            )
        elif intent == SearchIntent.COMMENTS:
            return self._generate_comments_query_plan(
                query_text, organisation_id, user_id, project_key, issue_key
            )
        elif intent == SearchIntent.METADATA:
            return self._generate_metadata_query_plan(
                query_text, organisation_id, user_id, project_key, issue_key
            )
        else:
            logger.warning(f"Unknown intent: {intent}, falling back to semantic search")
            return self._generate_semantic_query_plan(
                query_text, organisation_id, user_id, project_key, issue_key
            )
    
    def generate_fallback_plan(self, query_text: str, organisation_id: str, 
                             user_id: Optional[str] = None, project_key: Optional[str] = None, 
                             issue_key: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a fallback query plan for vector search.
        
        Args:
            query_text: The natural language query
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Fallback query plan dictionary
        """
        return self._generate_semantic_query_plan(
            query_text, organisation_id, user_id, project_key, issue_key
        )
    
    def _generate_semantic_query_plan(self, query_text: str, organisation_id: str, 
                                    user_id: Optional[str] = None, project_key: Optional[str] = None, 
                                    issue_key: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a query plan for semantic search (Pinecone).
        
        Args:
            query_text: The natural language query
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Semantic query plan dictionary
        """
        # Extract project key from query if not provided
        if not project_key:
            project_key_match = re.search(self.project_key_pattern, query_text)
            if project_key_match:
                extracted_key = project_key_match.group(1)
                # Check if it's a full issue key or just a project key
                if not re.match(self.issue_key_pattern, extracted_key):
                    project_key = extracted_key
        
        # Extract issue key from query if not provided
        if not issue_key:
            issue_key_match = re.search(self.issue_key_pattern, query_text)
            if issue_key_match:
                issue_key = issue_key_match.group(1)
                # Extract project key from issue key if not already set
                if not project_key:
                    project_key = issue_key.split('-')[0]
        
        # Build filter for Pinecone
        filter_dict = {"organisation_id": organisation_id}
        
        if project_key:
            filter_dict["project_key"] = project_key
        
        if issue_key:
            filter_dict["issue_key"] = issue_key
        
        # Clean query text by removing explicit keys
        cleaned_query = query_text
        if project_key:
            cleaned_query = re.sub(r'\b' + re.escape(project_key) + r'\b', '', cleaned_query)
        if issue_key:
            cleaned_query = re.sub(r'\b' + re.escape(issue_key) + r'\b', '', cleaned_query)
        
        # Remove common filler words for better semantic search
        cleaned_query = re.sub(r'\b(find|show|get|give me|display)\b', '', cleaned_query)
        cleaned_query = cleaned_query.strip()
        
        return {
            "query_text": cleaned_query,
            "original_query": query_text,
            "filter": filter_dict,
            "organisation_id": organisation_id,
            "user_id": user_id,
            "project_key": project_key,
            "issue_key": issue_key
        }
    
    def _generate_comments_query_plan(self, query_text: str, organisation_id: str, 
                                    user_id: Optional[str] = None, project_key: Optional[str] = None, 
                                    issue_key: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a query plan for comments search (PostgreSQL).
        
        Args:
            query_text: The natural language query
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Comments query plan dictionary
        """
        # Extract project key from query if not provided
        if not project_key:
            project_key_match = re.search(self.project_key_pattern, query_text)
            if project_key_match:
                extracted_key = project_key_match.group(1)
                # Check if it's a full issue key or just a project key
                if not re.match(self.issue_key_pattern, extracted_key):
                    project_key = extracted_key
        
        # Extract issue key from query if not provided
        if not issue_key:
            issue_key_match = re.search(self.issue_key_pattern, query_text)
            if issue_key_match:
                issue_key = issue_key_match.group(1)
                # Extract project key from issue key if not already set
                if not project_key:
                    project_key = issue_key.split('-')[0]
        
        # Extract user information
        user_email = None
        for pattern in self.user_patterns:
            user_match = re.search(pattern, query_text)
            if user_match:
                user_email = user_match.group(1)
                break
        
        # Extract date range
        date_range = self._extract_date_range(query_text)
        
        # Clean query text for full-text search
        cleaned_query = query_text
        if project_key:
            cleaned_query = re.sub(r'\b' + re.escape(project_key) + r'\b', '', cleaned_query)
        if issue_key:
            cleaned_query = re.sub(r'\b' + re.escape(issue_key) + r'\b', '', cleaned_query)
        if user_email:
            cleaned_query = re.sub(r'\b' + re.escape(user_email) + r'\b', '', cleaned_query)
        
        # Remove common filler words for better text search
        cleaned_query = re.sub(r'\b(find|show|get|give me|display|comments|comment)\b', '', cleaned_query)
        cleaned_query = cleaned_query.strip()
        
        return {
            "query_text": cleaned_query,
            "original_query": query_text,
            "organisation_id": organisation_id,
            "user_id": user_id,
            "project_key": project_key,
            "issue_key": issue_key,
            "user_email": user_email,
            "start_date": date_range.get("start_date"),
            "end_date": date_range.get("end_date")
        }
    
    def _generate_metadata_query_plan(self, query_text: str, organisation_id: str, 
                                    user_id: Optional[str] = None, project_key: Optional[str] = None, 
                                    issue_key: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a query plan for metadata search (Neo4j).
        
        Args:
            query_text: The natural language query
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Metadata query plan dictionary
        """
        # Extract project key from query if not provided
        if not project_key:
            project_key_match = re.search(self.project_key_pattern, query_text)
            if project_key_match:
                extracted_key = project_key_match.group(1)
                # Check if it's a full issue key or just a project key
                if not re.match(self.issue_key_pattern, extracted_key):
                    project_key = extracted_key
        
        # Extract issue key from query if not provided
        if not issue_key:
            issue_key_match = re.search(self.issue_key_pattern, query_text)
            if issue_key_match:
                issue_key = issue_key_match.group(1)
                # Extract project key from issue key if not already set
                if not project_key:
                    project_key = issue_key.split('-')[0]
        
        # Extract user information
        user_email = None
        for pattern in self.user_patterns:
            user_match = re.search(pattern, query_text)
            if user_match:
                user_email = user_match.group(1)
                break
        
        # Extract status
        status = None
        for pattern in self.status_patterns:
            status_match = re.search(pattern, query_text.lower())
            if status_match:
                status = status_match.group(1)
                break
        
        # Extract priority
        priority = None
        for pattern in self.priority_patterns:
            priority_match = re.search(pattern, query_text.lower())
            if priority_match:
                priority = priority_match.group(1)
                break
        
        # Extract date range
        date_range = self._extract_date_range(query_text)
        
        # Extract sprint
        sprint = None
        for pattern in self.sprint_patterns:
            sprint_match = re.search(pattern, query_text)
            if sprint_match:
                sprint = sprint_match.group(1)
                break
        
        # Build Cypher query parameters
        params = {
            "organisation_id": organisation_id,
            "user_id": user_id,
            "project_key": project_key,
            "issue_key": issue_key,
            "user_email": user_email,
            "status": status,
            "priority": priority,
            "start_date": date_range.get("start_date"),
            "end_date": date_range.get("end_date"),
            "sprint": sprint,
            "original_query": query_text
        }
        
        return params
    
    def _extract_date_range(self, query_text: str) -> Dict[str, Optional[str]]:
        """
        Extract date range from query text.
        
        Args:
            query_text: The natural language query
            
        Returns:
            Dictionary with start_date and end_date
        """
        date_range = {"start_date": None, "end_date": None}
        
        # Check for specific dates
        date_matches = []
        for pattern in self.date_patterns[:2]:  # Only the first two patterns are for specific dates
            for match in re.finditer(pattern, query_text):
                date_str = match.group(1)
                # Normalize date format
                try:
                    if '/' in date_str:
                        parts = date_str.split('/')
                        if len(parts) == 3:
                            month, day, year = parts
                            if len(year) == 2:
                                year = '20' + year
                            date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    date_matches.append((match.group(0), date_str))
                except Exception as e:
                    logger.warning(f"Error parsing date {date_str}: {e}")
        
        # Check for relative dates
        for match in re.finditer(self.date_patterns[2], query_text):
            try:
                amount = int(match.group(1))
                unit = match.group(2).lower()
                
                now = datetime.now()
                if unit in ['day', 'days']:
                    start_date = (now - timedelta(days=amount)).strftime('%Y-%m-%d')
                elif unit in ['week', 'weeks']:
                    start_date = (now - timedelta(weeks=amount)).strftime('%Y-%m-%d')
                elif unit in ['month', 'months']:
                    # Approximate months as 30 days
                    start_date = (now - timedelta(days=30*amount)).strftime('%Y-%m-%d')
                elif unit in ['year', 'years']:
                    # Approximate years as 365 days
                    start_date = (now - timedelta(days=365*amount)).strftime('%Y-%m-%d')
                else:
                    continue
                
                end_date = now.strftime('%Y-%m-%d')
                date_range["start_date"] = start_date
                date_range["end_date"] = end_date
                return date_range
            except Exception as e:
                logger.warning(f"Error parsing relative date: {e}")
        
        # Process specific dates
        if date_matches:
            for date_phrase, date_str in date_matches:
                if 'before' in date_phrase.lower():
                    date_range["end_date"] = date_str
                elif 'after' in date_phrase.lower():
                    date_range["start_date"] = date_str
                elif 'on' in date_phrase.lower():
                    date_range["start_date"] = date_str
                    date_range["end_date"] = date_str
        
        return date_range
    
    def _llm_query_planning(self, query_text: str, intents: Set[str], 
                          organisation_id: str, user_id: Optional[str] = None,
                          project_key: Optional[str] = None, 
                          issue_key: Optional[str] = None) -> Optional[Dict[str, Dict[str, Any]]]:
        """
        Generate query plans using LLM.
        
        Args:
            query_text: The natural language query
            intents: Set of detected intents
            organisation_id: Organization ID for access control
            user_id: Optional user ID for user-specific searches
            project_key: Optional project key to limit search scope
            issue_key: Optional issue key to limit search scope
            
        Returns:
            Dictionary mapping intent types to query plans, or None if LLM fails
        """
        try:
            # Prepare prompt for LLM
            intent_list = ", ".join(intents)
            prompt = f"""
            Generate query plans for the following user query for a Jira search system.
            
            Query: "{query_text}"
            Detected intents: {intent_list}
            
            For each intent, extract relevant parameters and create a structured query plan.
            
            Context:
            - organisation_id: {organisation_id}
            - user_id: {user_id or "None"}
            - project_key: {project_key or "None (extract from query if possible)"}
            - issue_key: {issue_key or "None (extract from query if possible)"}
            
            Respond with a JSON object where keys are intent types and values are query plans.
            Each query plan should include relevant parameters for that database type.
            
            Example response:
            {{
                "semantic": {{
                    "query_text": "performance issues",
                    "original_query": "Find performance issues in PROJ",
                    "filter": {{"organisation_id": "{organisation_id}", "project_key": "PROJ"}},
                    "organisation_id": "{organisation_id}",
                    "project_key": "PROJ"
                }},
                "metadata": {{
                    "organisation_id": "{organisation_id}",
                    "project_key": "PROJ",
                    "status": "open",
                    "priority": "high"
                }}
            }}
            """
            
            # Call LLM
            response = self.llm_client.generate_text(prompt)
            
            # Parse response
            import json
            try:
                result = json.loads(response)
                
                # Validate and clean up the result
                valid_intents = {SearchIntent.SEMANTIC, SearchIntent.COMMENTS, SearchIntent.METADATA, SearchIntent.HYBRID}
                cleaned_result = {}
                
                for intent, plan in result.items():
                    if intent in valid_intents and isinstance(plan, dict):
                        # Ensure required fields are present
                        if intent == SearchIntent.SEMANTIC:
                            if "query_text" not in plan:
                                plan["query_text"] = query_text
                            if "organisation_id" not in plan:
                                plan["organisation_id"] = organisation_id
                        elif intent == SearchIntent.COMMENTS:
                            if "organisation_id" not in plan:
                                plan["organisation_id"] = organisation_id
                        elif intent == SearchIntent.METADATA:
                            if "organisation_id" not in plan:
                                plan["organisation_id"] = organisation_id
                        
                        cleaned_result[intent] = plan
                
                if cleaned_result:
                    return cleaned_result
                else:
                    logger.warning("LLM returned no valid query plans")
                    return None
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse LLM response: {e}")
                return None
                
        except Exception as e:
            logger.error(f"Error in LLM query planning: {e}")
            return None