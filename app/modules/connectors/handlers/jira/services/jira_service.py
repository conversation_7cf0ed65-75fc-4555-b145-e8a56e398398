"""
gRPC service implementation for Jira connector.
"""

import structlog
from typing import List, Tuple

from app.grpc_ import jira_pb2
from app.grpc_ import jira_pb2_grpc
# from app.modules.connectors.handlers.jira.services.jira_service import JiraService
from app.modules.connectors.handlers.jira.services.jira_knowledge_graph import JiraKnowledgeGraphService

logger = structlog.get_logger()

class JiraService(jira_pb2_grpc.JiraServiceServicer):
    """
    gRPC service implementation for Jira connector.
    
    This service provides gRPC endpoints for Jira operations,
    including building knowledge graphs and checking job status.
    """
    
    def __init__(self):
        """Initialize the service."""
        # self.jira_service = JiraService()
        self.knowledge_graph_service = JiraKnowledgeGraphService()
    
    def buildKnowledgeGraph(self, request, context):
        """
        Build a knowledge graph for Jira projects.
        
        Args:
            request: BuildKnowledgeGraphRequest with organisation_id and project_keys
            context: gRPC context
            
        Returns:
            BuildKnowledgeGraphResponse with success status and message
        """
        try:
            logger.info(f"Received buildKnowledgeGraph request for organisation {request.organisation_id} for project {request.project_keys}")
            # Use the async version that schedules a job instead of waiting for completion
            success, message = self.knowledge_graph_service.build_knowledge_graph(
                organisation_id=request.organisation_id,
                project_keys=request.project_keys
            )
            # Create and return the response
            return jira_pb2.BuildKnowledgeGraphResponse(
                success=success,
                message=message
            )
        except Exception as e:
            logger.error(f"Error in buildKnowledgeGraph method: {e}")
            return jira_pb2.BuildKnowledgeGraphResponse(
                success=False,
                message=f"Error building knowledge graph: {str(e)}"
            )
    
    def scheduleKnowledgeGraphBuild(self, request, context):
        """
        Schedule a knowledge graph building job to run in the background.
        
        Args:
            request: ScheduleKnowledgeGraphBuildRequest with organisation_id, project_keys, and delay_seconds
            context: gRPC context
            
        Returns:
            ScheduleKnowledgeGraphBuildResponse with success status, message, and job_id
        """
        try:
            logger.info(f"Received scheduleKnowledgeGraphBuild request for organisation {request.organisation_id}")

            if not self.kg_worker.running:
                self.kg_worker.start()
                logger.info("Started Jira knowledge graph worker")
            
            # Call the existing service method
            job_id = self.kg_worker.schedule_job(
                organisation_id=request.organisation_id,
                projects_key=list(request.project_keys),
                delay_seconds=getattr(request, 'delay_seconds', 0)
            )
            
            # Create and return the response
            return jira_pb2.ScheduleKnowledgeGraphBuildResponse(
                success=True,
                message=f"Knowledge graph build scheduled with job ID: {job_id}",
                job_id=job_id
            )
        except Exception as e:
            logger.error(f"Error in scheduleKnowledgeGraphBuild gRPC method: {e}")
            return jira_pb2.ScheduleKnowledgeGraphBuildResponse(
                success=False,
                message=f"Error scheduling knowledge graph build: {str(e)}",
                job_id=""
            )
    
    def getKnowledgeGraphBuildStatus(self, request, context):
        """
        Get the status of a knowledge graph building job.
        
        Args:
            request: GetKnowledgeGraphBuildStatusRequest with job_id
            context: gRPC context
            
        Returns:
            GetKnowledgeGraphBuildStatusResponse with job status details
        """
        try:
            logger.info(f"Received getKnowledgeGraphBuildStatus request for job {request.job_id}")
            
            # Call the existing service method
            progress_data = self.kg_worker.get_job_progress(request.job_id)
            
            if not progress_data:
                # Job not found
                return jira_pb2.GetKnowledgeGraphBuildStatusResponse(
                    success=False,
                    message="Job not found",
                    status="not_found",
                    progress_percentage=0,
                    projects_processed=0,
                    issues_processed=0,
                    comments_processed=0,
                    error_count=0,
                    started_at="",
                    last_updated_at=""
                )
            
            # Extract status information from progress data
            # status = progress_data.get("status", "unknown")
            # progress = progress_data.get("progress", 0.0)
            # error = progress_data.get("error", "")
            # projects_processed = progress_data.get("projects_processed", 0)
            # total_projects = progress_data.get("total_projects", 0)
            
            # Create and return the response
            return jira_pb2.GetKnowledgeGraphBuildStatusResponse(
                    success=True,
                    message="Status retrieved successfully",
                    status=progress_data.get("status", "unknown"),
                    progress_percentage=progress_data.get("percent_complete", 0),
                    projects_processed=progress_data.get("total_projects", 0),
                    issues_processed=progress_data.get("total_issues_processed", 0),
                    comments_processed=progress_data.get("total_comments_processed", 0),
                    error_count=progress_data.get("error_count", 0),
                    started_at=progress_data.get("started_at", ""),
                    last_updated_at=progress_data.get("last_updated_at", "")
                )
        except Exception as e:
            logger.error(f"Error in getKnowledgeGraphBuildStatus gRPC method: {e}")
            return jira_pb2.GetKnowledgeGraphBuildStatusResponse(
                success=False,
                message=f"Error getting build status: {str(e)}",
                status="error",
                progress_percentage=0,
                projects_processed=0,
                issues_processed=0,
                comments_processed=0,
                error_count=0,
                started_at="",
                last_updated_at=""
            )
        
    async def build_knowledge_graph_async(self, organisation_id: str, projects_key: List[str] = ["*"]) -> Tuple[bool, str, str]:
        """
        Start a knowledge graph building job asynchronously and return immediately.
        This will start the worker if it's not already running.
        
        Args:
            organisation_id: ID of the organization
            projects_key: List of project keys to index. If ["*"], all projects will be indexed.
            
        Returns:
            Tuple of (success, message, job_id)
        """
        try:
            # Schedule the job (this will start the worker if needed)
            job_id = self.scheduleKnowledgeGraphBuild(
                organisation_id=organisation_id,
                projects_key=projects_key
            )
            
            return True, f"Knowledge graph build scheduled with job ID: {job_id}", job_id
        except Exception as e:
            logger.error(f"Error scheduling asynchronous Jira knowledge graph build: {e}")
            return False, f"Error scheduling knowledge graph build: {str(e)}", ""