from sqlalchemy import Column, String, DateTime, Index
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from app.db.init_db import Base


class JiraComment(Base):
    """
    Model for storing Jira comments in PostgreSQL database.
    
    This model stores comments from Jira issues with their metadata
    for search and analysis purposes.
    """
    __tablename__ = "jira_comments"

    # Primary key
    uuid = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Organization and project identifiers
    organisation_id = Column(String, nullable=False, index=True)
    project_key = Column(String, nullable=False, index=True)
    issue_key = Column(String, nullable=False, index=True)
    
    # Comment metadata
    comment_date = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    comment = Column(String, nullable=False)
    
    # User information
    user_email = Column(String, nullable=True)
    user_id = Column(String, nullable=True, index=True)
    
    # Create composite indexes for common query patterns
    __table_args__ = (
        Index('idx_jira_comments_org_project', 'organisation_id', 'project_key'),
        Index('idx_jira_comments_issue', 'organisation_id', 'project_key', 'issue_key'),
        Index('idx_jira_comments_user', 'organisation_id', 'user_id'),
    )
    
    def __repr__(self):
        return f"<JiraComment {self.issue_key} - {self.uuid}>"