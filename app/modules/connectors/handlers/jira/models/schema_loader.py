"""
Schema loader for Jira connector graph schema.
"""

import os
import yaml
from pathlib import Path
import structlog

logger = structlog.get_logger()

class JiraGraphSchema:
    """
    Loads and provides access to the Jira connector graph schema.
    
    This class loads the schema definition from the YAML file and provides
    methods to access node labels, relationship types, and validate properties.
    It also handles references to external nodes like User from the organization schema.
    """
    
    def __init__(self, schema_path=None, org_schema_path=None):
        """
        Initialize the schema loader.
        
        Args:
            schema_path: Path to the schema YAML file (optional)
            org_schema_path: Path to the organization schema YAML file (optional)
        """
        # Default to module-relative path if no path provided
        if schema_path is None:
            schema_path = os.path.join(os.path.dirname(__file__), "jira_schema.yml")
        
        # Default organization schema path
        if org_schema_path is None:
            org_schema_path = os.path.join(
                os.path.dirname(__file__), 
                "../../../../organisation/models/graph_schema.yml"
            )
        
        try:
            self.schema = yaml.safe_load(Path(schema_path).read_text())
            logger.info("Loaded Jira graph schema", schema_version=self.schema.get("version"))
            
            # Try to load organization schema for User node
            try:
                self.org_schema = yaml.safe_load(Path(org_schema_path).read_text())
                logger.info("Loaded organization graph schema for reference")
            except Exception as e:
                logger.warning("Failed to load organization schema, User node properties may be incomplete", error=str(e))
                self.org_schema = {"nodes": {}}
                
        except Exception as e:
            logger.error("Failed to load Jira graph schema", error=str(e))
            self.schema = {"nodes": {}, "relationships": {}}
            self.org_schema = {"nodes": {}}
    
    def get_node_labels(self):
        """
        Get all node labels defined in the schema.
        
        Returns:
            List of node label strings
        """
        labels = list(self.schema.get("nodes", {}).keys())
        
        return labels
    
    def get_relationship_types(self):
        """
        Get all relationship types defined in the schema.
        
        Returns:
            List of relationship type strings
        """
        return list(self.schema.get("relationships", {}).keys())
    
    def get_node_properties(self, label):
        """
        Get all properties for a specific node label.
        
        Args:
            label: Node label string
            
        Returns:
            Dictionary of property definitions or empty dict if label not found
        """
        # Check Jira schema first
        if label in self.schema.get("nodes", {}):
            return self.schema.get("nodes", {}).get(label, {}).get("properties", {})
        
        return {}
    
    def get_relationship_properties(self, rel_type):
        """
        Get all properties for a specific relationship type.
        
        Args:
            rel_type: Relationship type string
            
        Returns:
            Dictionary of property definitions or empty dict if type not found
        """
        return self.schema.get("relationships", {}).get(rel_type, {}).get("properties", {})
    
    def get_relationship_endpoints(self, rel_type):
        """
        Get the source and target node labels for a relationship type.
        
        Args:
            rel_type: Relationship type string
            
        Returns:
            Tuple of (from_label, to_label) or (None, None) if not found
        """
        rel_def = self.schema.get("relationships", {}).get(rel_type, {})
        return rel_def.get("from"), rel_def.get("to")

# Initialize the schema
jira_schema = JiraGraphSchema()