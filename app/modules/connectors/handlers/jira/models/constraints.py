import structlog
from app.services.neo4j_service import execute_write_query

logger = structlog.get_logger()

def create_jira_indexes_and_constraints():
    """
    Create Neo4j indexes and constraints for Jira connector entities.
    """
    queries = [
        # Constraints (for required, unique IDs)
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (n:JiraProject)
        REQUIRE n.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (n:JiraIssue)
        REQUIRE n.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (n:<PERSON>raComment)
        REQUIRE n.id IS UNIQUE
        """,

        # Indexes for commonly queried fields
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraProject)
        ON (n.key)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraProject)
        ON (n.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraProject)
        ON (n.name)
        """,

        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraIssue)
        ON (n.key)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraIssue)
        ON (n.project_key)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraIssue)
        ON (n.parent_key)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraIssue)
        ON (n.status)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraIssue)
        ON (n.issue_type)
        """,

        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraComment)
        ON (n.issue_key)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (n:JiraComment)
        ON (n.created_at)
        """
    ]

    for query in queries:
        try:
            execute_write_query(query)
        except Exception as e:
            logger.warning(f"Error creating constraint/index: {e}")

    logger.info("Jira connector Neo4j indexes and constraints created.")
