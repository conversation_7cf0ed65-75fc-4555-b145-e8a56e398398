# Jira Connector Schema
version: 1.0
description: "Schema definition for Jira connector entities and relationships"

nodes:
  JiraUser:
    description: "Represent a Jira user"
    properties:
      id:
        type: string
        required: true
        description: "Jira account ID"
      name:
        type: string
        required: true
        description: "Jira user display name"
      email:
        type: string
        description: "Jira user email"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this user"
      created_at:
        type: timestamp
        description: "User creation timestamp"
      updated_at:
        type: timestamp
        description: "User last updated timestamp"

  AssignedTasks:
    description: "Represents the assigned tasks of a Jira user"
    properties:
      id:
        type: string
        required: true
        description: "Unique ID for the assigned task"
      user_id:
        type: string
        required: true
        description: "ID of the user"
      project_key:
        type: string
        required: true
        description: "Key of the project"

  ReportedTasks:
    description: "Represents the reported tasks of a Jira user"
    properties:
      id:
        type: string
        required: true
        description: "Unique ID for the reported task"
      user_id:
        type: string
        required: true
        description: "ID of the user"
      project_key:
        type: string
        required: true
        description: "Key of the project"

  AccessProjects:
    description: "Represents the projects a user has access to"
    properties:
      id:
        type: string
        required: true
        description: "Unique ID for the access relationship"
      user_id:
        type: string
        required: true
        description: "ID of the user"

  Project:
    description: "Represents a Jira project"
    properties:
      key:
        type: string
        required: true
        description: "Jira project key (e.g., PROJ)"
      name:
        type: string
        description: "Project name"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this project"
      id:
        type: string
        required: true
        description: "Jira project ID"
      entity_id:
        type: string
        description: "Entity ID of the project in Jira"
      uuid:
        type: string
        description: "UUID of the project in Jira"
      created:
        type: timestamp
        description: "Project creation timestamp"
      updated:
        type: timestamp
        description: "Project last updated timestamp"

  Backlog:
    description: "Represents the backlog of a project"
    properties:
      project_key:
        type: string
        required: true
        description: "Key of the project"
      id:
        type: string
        required: true
        description: "Unique ID for the backlog"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this project"

  Issue:
    description: "Represents a Jira issue"
    properties:
      id:
        type: string
        required: true
        description: "Jira issue ID"
      key:
        type: string
        required: true
        description: "Jira issue key (e.g., PROJ-123)"
      project_key:
        type: string
        required: true
        description: "Key of the project this issue belongs to"
      parent_key:
        type: string
        description: "Parent Key of the issue belongs to"
      issue_type:
        type: string
        description: "Type of issue (e.g., Epic, Sprint, Task, Story, sub-task)"
      status:
        type: string
        description: "Current status of the issue"
      priority:
        type: string
        description: "Issue priority"
      labels:
        type: string
        description: "Labels associated with the issue"
      due_date:
        type: string
        description: "Last date for the task to finish"
      progress:
        type: string
        description: "Current progress of the issue"
      created_at:
        type: timestamp
        description: "Issue creation timestamp"
      updated_at:
        type: timestamp
        description: "Issue last updated timestamp"

  Sprint:
    description: "Represents a Jira sprint"
    properties:
      id:
        type: string
        required: true
        description: "Jira sprint ID"
      name:
        type: string
        description: "Sprint name"
      state:
        type: string
        description: "Sprint state (future, active, closed)"
      project_key:
        type: string
        required: true
        description: "Key of the project this sprint belongs to"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this sprint"
      start_date:
        type: string
        description: "Sprint start date"
      end_date:
        type: string
        description: "Sprint end date"
      complete_date:
        type: string
        description: "Sprint completion date"
      goal:
        type: string
        description: "Sprint goal"
      board_id:
        type: string
        description: "ID of the board this sprint belongs to"
      created_at:
        type: timestamp
        description: "Sprint creation timestamp"
      updated_at:
        type: timestamp
        description: "Sprint last updated timestamp"

relationships:
  HAS_ISSUE:
    from: Project
    to: Issue
    description: "Project contains issues"
    direction: "->"
    properties:
      issue_type:
        type: string
        description: "Type of issue like Story, Task, Epid, Bug"
      project_key:
        type: string
        required: true
        description: "Key of the project this issue belongs to"
      due_date:
        type: string
        description: "Due date for the issue"
      story_point:
        type: string
        description: "Story points assigned to the issue"
      status:
        type: string
        description: "Current status of the issue"
      priority:
        type: string
        description: "Priority of the issue"
      created_at:
        type: timestamp
        description: "When the issue was created in the project"

  HAS_USER:
    from: Source
    to: JiraUser
    description: "Source has Jira users"
    direction: "->"

  REPORTED_TO:
    from: Issue
    to: User
    description: "Issue is reported to a user (e.g. the reporter or stakeholder)"
    direction: "->"
    properties:
      account_id:
        type: string
        description: "Jira account ID of the user"
      created_at:
        type: timestamp
        description: "When the issue was reported"

  ASSIGNED_TO:
    from: Issue
    to: User
    description: "User is assigned to an issue"
    direction: "->"
    properties:
      account_id:
        type: string
        description: "Jira account ID of the user"

  HAS_ACCOUNT:
    from: User
    to: JiraUser
    description: "User has a Jira account"
    direction: "->"
    properties:
      type:
        type: string
        required: true
        description: "Type of account like jira, github etc"
      created_at:
        type: timestamp
        description: "When the user was created in Jira"

  HAS_PROJECT:
    from: Source
    to: Project
    description: "Source has Jira projects"
    direction: "->"

  HAS_ACCESS:
    from: JiraUser
    to: Project
    description: "User has access to the project"
    direction: "->"
    properties:
      access_type:
        type: string
        description: "User access type in the project"

  HAS_ASSIGNED_TASKS:
    from: JiraUser
    to: Assigned_tasks
    description: "Jira user has assigned tasks"
    direction: "->"

  HAS_REPORTED_TASKS:
    from: JiraUser
    to: Reported_tasks
    description: "Jira user has reported tasks"
    direction: "->"

  HAS_PARENT:
    from: Issue
    to: Issue
    description: "Parent ticket of a task/sub-task"
    direction: "->"

  HAS_SPRINT:
    from: Project
    to: Sprint
    description: "Project contains sprints"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the sprint was created in the project"

  IN_SPRINT:
    from: Issue
    to: Sprint
    description: "Issue belongs to a sprint"
    direction: "->"
    properties:
      status:
        type: string
        description: "Sprint status (open/closed) when the issue was in this sprint"
      created_at:
        type: timestamp
        description: "When the issue was added to the sprint"