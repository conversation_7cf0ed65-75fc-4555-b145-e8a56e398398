"""
Repository for Jira connector graph queries.
"""

from app.modules.connectors.handlers.jira.models.schema_loader import jira_schema as schema
from app.modules.organisation.models.schema_loader import schema as org_schema
import structlog

logger = structlog.get_logger()

class JiraQuery:
    def __init__(self):
        # Get node labels
        org_label = org_schema.get_node_labels()
        labels = schema.get_node_labels()
        self.jira_user_label = labels[0]      # "JiraUser"
        self.assigned_tasks = labels[1]       # "AssignedTasks"
        self.reported_tasks = labels[2]       # "ReportedTasks"
        self.access_projects = labels[3]      # "AccessProjects"
        self.project_label = labels[4]        # "Project"
        self.backlog_label = labels[5]       # "Backlog"
        self.issue_label = labels[6]          # "Issue"
        self.org_label = org_label[0]         # "Organisation"
        self.user_label = org_label[1]        # "User"
        self.sprint_label = "Sprint"          # Adding Sprint label
        self.source_label = "Source"
        
        # Get relationship types
        rels = schema.get_relationship_types()
        self.has_issue_rel = rels[0]               # "HAS_ISSUE"
        self.has_user = rels[1]                    # "HAS_USER"
        # self.has_comment_rel = rels[1]             # "HAS_COMMENT"
        # self.authored_comment_rel = rels[2]        # "HAS_COMMENTED"
        self.reported_to_rel = rels[2]             # "REPORTED_TO"
        self.assigned_to_rel = rels[3]             # "ASSIGNED_TO"
        self.has_account_rel = rels[4]             # "HAS_ACCOUNT"
        self.has_project_rel = rels[5]             # "HAS_PROJECT"
        self.has_access_rel = rels[6]              # "HAS_ACCESS"
        self.has_assigned_tasks_rel = rels[7]     # "HAS_ASSIGNED_TASKS"
        self.has_reported_tasks_rel = rels[8]     # "HAS_REPORTED_TASKS"
        self.has_parent_rel = rels[9]             # "HAS_PARENT"
        self.has_sprint_rel = rels[10]            # "HAS_SPRINT"
        self.in_sprint_rel = rels[11]             # "IN_SPRINT"
        
        # Additional relationship types
        self.has_source_rel = "HAS_SOURCE"
        self.belongs_to_org_rel = "BELONGS_TO_ORGANISATION"
        self.reported_issue_rel = "REPORTED"
        self.assigned_to_issue_rel = "ASSIGNED_TO"
        self.mentioned_in_comment_rel = "MENTIONED_IN"
        self.blocks_rel = "BLOCKS"
        self.is_blocked_by_rel = "IS_BLOCKED_BY"

    
    @property
    def GET_JIRA_SOURCE(self):
        """
            Get the JIRA source node of the organisation
        """
        return f"""
             MATCH (o:{self.org_label}{{id: $org_id}})-[:{self.has_source_rel}]->(s:{self.source_label})
             WHERE s.type = $source_type
             RETURN s
        """
    @property
    def CREATE_JIRA_PROJECTS_BATCH(self):
        """
        Create multiple JIRA Project nodes in batch
        """
        return f"""
            MATCH (source:{self.source_label} {{organisation_id: $org_id, type: $type}})
            UNWIND $projects AS project
            MERGE (p:{self.project_label} {{key: project.key}})
            ON CREATE SET 
                p.name = project.name,
                p.id = project.id,
                p.entity_id = project.entity_id,
                p.uuid = project.uuid,
                p.organisation_id = $org_id,
                p.created_at = datetime(),
                p.updated_at = datetime()
            ON MATCH SET
                p.name = project.name,
                p.id = project.id,
                p.entity_id = project.entity_id,
                p.uuid = project.uuid,
                p.updated_at = datetime()
            MERGE (source)-[:{self.has_project_rel}]->(p)
            RETURN count(p) as created_count
        """

    @property
    def CREATE_JIRA_PROJECT(self):
        """
        Create a single JIRA Project node (kept for fallback)
        """
        return f"""
            MATCH (source:{self.source_label} {{organisation_id: $org_id, type: $type}})
            MERGE (p:{self.project_label} {{key: $key, organisation_id: $org_id}})
            ON CREATE SET 
                p.name = $name,
                p.id = $id,
                p.entity_id = $entity_id,
                p.uuid = $uuid,
                p.organisation_id = $org_id,
                p.created_at = datetime(),
                p.updated_at = datetime()
            ON MATCH SET
                p.name = $name,
                p.id = $id,
                p.entity_id = $entity_id,
                p.uuid = $uuid,
                p.updated_at = datetime()
            MERGE (source)-[:{self.has_project_rel}]->(p)
            RETURN p
        """
    
    @property
    def CREATE_OR_UPDATE_ISSUE(self):
        """
        Create or update a Jira issue node without linking to project.
        Project linking will be handled separately based on sprint availability.
        """
        return f"""
            // Create or update the issue
            MERGE (i:{self.issue_label} {{id: $id, key: $key}})
            ON CREATE SET
                i.organisation_id = $organisation_id,
                i.project_key = $project_key,
                i.issue_type = $issue_type,
                i.status = $status,
                i.priority = $priority,
                i.assignee_account_id = $assignee_account_id,
                i.reporter_account_id = $reporter_account_id,
                i.created_at = $created_at,
                i.updated_at = $updated_at
            ON MATCH SET
                i.issue_type = $issue_type,
                i.status = $status,
                i.priority = $priority,
                i.assignee_account_id = $assignee_account_id,
                i.reporter_account_id = $reporter_account_id,
                i.updated_at = $updated_at

            RETURN i
        """
    
    @property
    def CREATE_OR_UPDATE_COMMENT(self):
        """
        Create or update a Jira comment node and link it to the issue.
        """
        return f"""
            // Match the issue
            MATCH (i:{self.issue_label} {{key: $issue_key, organisation_id: $organisation_id}})
            
            // Create or update the comment
            MERGE (c:{self.comment_label} {{id: $id}})
            ON CREATE SET
                c.organisation_id = $organisation_id,
                c.issue_key = $issue_key,
                c.author_account_id = $author_account_id,
                c.content = $content,
                c.created_at = $created_at,
                c.updated_at = $updated_at
            ON MATCH SET
                c.content = $content,
                c.updated_at = $updated_at
            
            // Create relationship to issue if it doesn't exist
            MERGE (i)-[r:{self.has_comment_rel}]->(c)
            ON CREATE SET r.created_at = $created_at

            RETURN c
        """
    
    @property
    def CREATE_OR_UPDATE_USER_WITH_JIRA_ACCOUNT(self):
        """
        Create or update a User node with Jira account information.
        This uses the existing User node from the organization schema.
        Instead of creating a self-relationship, it adds the Jira account ID
        as a property to the user node.
        """
        return f"""
            // Find existing user by email if available
            OPTIONAL MATCH (u:{self.user_label} {{email: $email, organisation_id: $organisation_id}})
            
            // If user exists, update with Jira account ID as a property
            FOREACH (x IN CASE WHEN u IS NOT NULL THEN [1] ELSE [] END |
                SET u.jira_account_id = $account_id,
                    u.jira_display_name = $display_name,
                    u.updated_at = $timestamp
            )
            
            // If user doesn't exist and we have an email, create a new user
            FOREACH (x IN CASE WHEN u IS NULL AND $email <> '' THEN [1] ELSE [] END |
                CREATE (u:{self.user_label} {{
                    id: $user_id,
                    email: $email,
                    organisation_id: $organisation_id,
                    name: $display_name,
                    jira_account_id: $account_id,
                    jira_display_name: $display_name,
                    creation_type: 'jira_sync',
                    created_at: $timestamp,
                    updated_at: $timestamp
                }})
            )
            
            // If user doesn't exist and we don't have an email but have account_id, create a placeholder user
            FOREACH (x IN CASE WHEN u IS NULL AND $email = '' AND $account_id <> '' THEN [1] ELSE [] END |
                CREATE (u:{self.user_label} {{
                    id: $user_id,
                    email: concat('jira_', $account_id, '@placeholder.com'),
                    organisation_id: $organisation_id,
                    name: $display_name,
                    jira_account_id: $account_id,
                    jira_display_name: $display_name,
                    creation_type: 'jira_sync',
                    created_at: $timestamp,
                    updated_at: $timestamp
                }})
            )
            
            // Return the user if found or created
            WITH u
            WHERE u IS NOT NULL
            RETURN u
        """
    
    @property
    def LINK_ISSUE_TO_USERS(self):
        """
        Link an issue to its reporter and assignee.
        """
        return f"""
            // Match the issue
            MATCH (i:{self.issue_label} {{key: $key, organisation_id: $organisation_id}})
            
            // Reporter relationship - find user with the Jira account ID
            WITH i
            OPTIONAL MATCH (reporter:{self.user_label})
            WHERE reporter.jira_account_id = $reporter_account_id AND reporter.organisation_id = $organisation_id
            
            FOREACH (x IN CASE WHEN reporter IS NOT NULL THEN [1] ELSE [] END |
                MERGE (reporter)-[rel:{self.reported_issue_rel}]->(i)
                ON CREATE SET
                    rel.account_id = $reporter_account_id,
                    rel.created_at = $created_at
            )
            
            // Assignee relationship - find user with the Jira account ID
            WITH i
            OPTIONAL MATCH (assignee:{self.user_label})
            WHERE assignee.jira_account_id = $assignee_account_id AND assignee.organisation_id = $organisation_id
            
            FOREACH (x IN CASE WHEN assignee IS NOT NULL THEN [1] ELSE [] END |
                MERGE (assignee)-[rel:{self.assigned_to_issue_rel}]->(i)
                ON CREATE SET
                    rel.account_id = $assignee_account_id,
                    rel.assigned_at = $updated_at
            )
            
            RETURN i
        """
    
    @property
    def LINK_COMMENT_TO_AUTHOR(self):
        """
        Link a comment to its author.
        """
        return f"""
            // Match the comment
            MATCH (c:{self.comment_label} {{id: $id, organisation_id: $organisation_id}})
            
            // Author relationship - find user with the Jira account ID
            WITH c
            OPTIONAL MATCH (author:{self.user_label})
            WHERE author.jira_account_id = $author_account_id AND author.organisation_id = $organisation_id
            
            FOREACH (x IN CASE WHEN author IS NOT NULL THEN [1] ELSE [] END |
                MERGE (author)-[rel:{self.authored_comment_rel}]->(c)
                ON CREATE SET
                    rel.account_id = $author_account_id,
                    rel.created_at = $created_at
            )
            
            RETURN c
        """
    
    @property
    def LINK_COMMENT_MENTIONS(self):
        """
        Link users mentioned in a comment.
        """
        return f"""
            // Match the comment
            MATCH (c:{self.comment_label} {{id: $id, organisation_id: $organisation_id}})
            
            // Process each mentioned user
            UNWIND $mentioned_users AS mentioned
            OPTIONAL MATCH (u:{self.user_label})
            WHERE u.jira_account_id = mentioned.accountId AND u.organisation_id = $organisation_id
            
            FOREACH (x IN CASE WHEN u IS NOT NULL THEN [1] ELSE [] END |
                MERGE (u)-[rel:{self.mentioned_in_comment_rel}]->(c)
                ON CREATE SET
                    rel.account_id = mentioned.accountId,
                    rel.mentioned_at = $created_at
            )
            
            RETURN c, count(u) as mention_count
        """
    
    @property
    def GET_PROJECT_WITH_ISSUES(self):
        """
        Get a project with all its issues.
        """
        return f"""
            MATCH (p:{self.project_label} {{key: $key, organisation_id: $organisation_id}})
            OPTIONAL MATCH (p)-[:{self.has_issue_rel}]->(i:{self.issue_label})
            RETURN p, collect(i) as issues
        """
    
    @property
    def GET_ISSUE_WITH_COMMENTS(self):
        """
        Get an issue with all its comments.
        """
        return f"""
            MATCH (i:{self.issue_label} {{key: $key, organisation_id: $organisation_id}})
            OPTIONAL MATCH (i)-[:{self.has_comment_rel}]->(c:{self.comment_label})
            OPTIONAL MATCH (author:{self.user_label})-[:{self.authored_comment_rel}]->(c)
            RETURN i, 
                collect(DISTINCT {{
                    comment: c,
                    author: author
                }}) as comments
        """
    
    @property
    def GET_USER_ISSUES(self):
        """
        Get all issues assigned to or reported by a user.
        """
        return f"""
            MATCH (u:{self.user_label} {{id: $user_id, organisation_id: $organisation_id}})
            OPTIONAL MATCH (u)-[:{self.assigned_to_issue_rel}]->(i1:{self.issue_label})
            OPTIONAL MATCH (u)-[:{self.reported_issue_rel}]->(i2:{self.issue_label})
            RETURN u,
                collect(DISTINCT i1) as assigned_issues,
                collect(DISTINCT i2) as reported_issues
        """
    
    @property
    def GET_USER_MENTIONED_IN(self):
        """
        Get all comments where a user is mentioned.
        """
        return f"""
            MATCH (u:{self.user_label} {{id: $user_id, organisation_id: $organisation_id}})
            MATCH (u)-[:{self.mentioned_in_comment_rel}]->(c:{self.comment_label})
            MATCH (i:{self.issue_label})-[:{self.has_comment_rel}]->(c)
            RETURN u, collect(DISTINCT {{comment: c, issue: i}}) as mentions
        """
    
    @property
    def GET_USER_BY_JIRA_ACCOUNT(self):
        """
        Find a user by their Jira account ID.
        """
        return f"""
            MATCH (u:{self.user_label})
            WHERE u.jira_account_id = $account_id AND u.organisation_id = $organisation_id
            RETURN u
        """
    
    @property
    def DELETE_PROJECT_WITH_ISSUES(self):
        """
        Delete a project and all its issues and comments.
        """
        return f"""
            MATCH (p:{self.project_label} {{key: $key, organisation_id: $organisation_id}})
            OPTIONAL MATCH (p)-[:{self.has_issue_rel}]->(i:{self.issue_label})
            OPTIONAL MATCH (i)-[:{self.has_comment_rel}]->(c:{self.comment_label})
            
            // Delete all relationships first
            OPTIONAL MATCH (c)-[cr]->()
            DELETE cr
            
            OPTIONAL MATCH ()-[cr]->(c)
            DELETE cr
            
            OPTIONAL MATCH (i)-[ir]->()
            DELETE ir
            
            OPTIONAL MATCH ()-[ir]->(i)
            DELETE ir
            
            OPTIONAL MATCH (p)-[pr]->()
            DELETE pr
            
            OPTIONAL MATCH ()-[pr]->(p)
            DELETE pr
            
            // Then delete the nodes
            DETACH DELETE c, i, p
            
            RETURN count(p) as deleted_projects, count(i) as deleted_issues, count(c) as deleted_comments
        """
        
    @property
    def CREATE_OR_UPDATE_SPRINT(self):
        """
        Create or update a Jira sprint node and link it to the project.
        """
        return f"""
            // First create or update the sprint
            MERGE (s:{self.sprint_label} {{id: $id, organisation_id: $organisation_id}})
            ON CREATE SET
                s.organisation_id = $organisation_id,
                s.organisation_id = $organisation_id,
                s.project_key = $project_key,
                s.name = $name,
                s.state = $state,
                s.start_date = $start_date,
                s.end_date = $end_date,
                s.complete_date = $complete_date,
                s.goal = $goal,
                s.board_id = $board_id,
                s.created_at = datetime(),
                s.updated_at = datetime()
            ON MATCH SET
                s.name = $name,
                s.state = $state,
                s.start_date = $start_date,
                s.end_date = $end_date,
                s.complete_date = $complete_date,
                s.goal = $goal,
                s.board_id = $board_id,
                s.updated_at = datetime()

            // Then match the project and create relationship
            WITH s
            MATCH (p:{self.project_label} {{key: $project_key, organisation_id: $organisation_id}})
            MERGE (p)-[r:{self.has_sprint_rel}]->(s)
            ON CREATE SET r.created_at = datetime()

            RETURN s, p
        """

    @property
    def CHECK_PROJECT_EXISTS(self):
        """
        Check if a project exists.
        """
        return f"""
            MATCH (p:{self.project_label} {{key: $project_key, organisation_id: $organisation_id}})
            RETURN p
        """

    @property
    def LINK_ISSUE_TO_SPRINT(self):
        """
        Link an issue to a sprint with sprint status.
        """
        return f"""
            // Match the issue and sprint using org_id (consistent with creation)
            MATCH (i:{self.issue_label} {{key: $issue_key, organisation_id: $organisation_id}})
            MATCH (s:{self.sprint_label} {{id: $sprint_id, organisation_id: $organisation_id}})

            // Create relationship if it doesn't exist, including sprint status
            MERGE (i)-[r:{self.in_sprint_rel}]->(s)
            ON CREATE SET
                r.created_at = datetime(),
                r.status = $sprint_status
            ON MATCH SET
                r.status = $sprint_status

            RETURN i, s
        """
    
    @property
    def LINK_ISSUE_TO_PROJECT(self):
        """
        Link an issue directly to a project (when no sprint is available).
        """
        return f"""
            // Match the issue and project using org_id (consistent with project creation)
            MATCH (i:{self.issue_label} {{key: $issue_key, organisation_id: $organisation_id}})
            MATCH (p:{self.project_label} {{key: $project_key, organisation_id: $organisation_id}})

            // Create relationship if it doesn't exist
            MERGE (p)-[r:{self.has_issue_rel}]->(i)
            ON CREATE SET r.created_at = datetime()

            RETURN i, p
        """
    
    @property
    def CREATE_ISSUE_BLOCKER_RELATIONSHIP(self):
        """
        Create a blocker relationship between two issues.
        """
        return f"""
            // Match the issues
            MATCH (i1:{self.issue_label} {{key: $blocker_key, organisation_id: $organisation_id}})
            MATCH (i2:{self.issue_label} {{key: $blocked_key, organisation_id: $organisation_id}})
            
            // Create blocker relationships
            MERGE (i1)-[r1:{self.blocks_rel}]->(i2)
            ON CREATE SET r1.created_at = datetime()
            
            MERGE (i2)-[r2:{self.is_blocked_by_rel}]->(i1)
            ON CREATE SET r2.created_at = datetime()
            
            RETURN i1, i2
        """
    
    @property
    def GET_SPRINT_WITH_ISSUES(self):
        """
        Get a sprint with all its issues.
        """
        return f"""
            MATCH (s:{self.sprint_label} {{id: $sprint_id, organisation_id: $organisation_id}})
            OPTIONAL MATCH (i:{self.issue_label})-[:{self.in_sprint_rel}]->(s)
            RETURN s, collect(i) as issues
        """
    
    @property
    def GET_PROJECT_SPRINTS(self):
        """
        Get all sprints for a project.
        """
        return f"""
            MATCH (p:{self.project_label} {{key: $project_key, organisation_id: $organisation_id}})
            MATCH (p)-[:{self.has_sprint_rel}]->(s:{self.sprint_label})
            RETURN p, collect(s) as sprints
        """
    
    @property
    def GET_ISSUE_BLOCKERS(self):
        """
        Get all blockers for an issue.
        """
        return f"""
            MATCH (i:{self.issue_label} {{key: $issue_key, organisation_id: $organisation_id}})
            OPTIONAL MATCH (i)-[:{self.is_blocked_by_rel}]->(blocker:{self.issue_label})
            RETURN i, collect(blocker) as blockers
        """
    
    @property
    def GET_ISSUE_BLOCKING(self):
        """
        Get all issues that are blocked by this issue.
        """
        return f"""
            MATCH (i:{self.issue_label} {{key: $issue_key, organisation_id: $organisation_id}})
            OPTIONAL MATCH (i)-[:{self.blocks_rel}]->(blocked:{self.issue_label})
            RETURN i, collect(blocked) as blocking
        """
    
    @property
    def CREATE_USER_NODE_WITH_EMAIL(self):
        """
        Create a User node in Neo4j with email.
        """
        return f"""
            MERGE (u:{self.jira_user_label} {{id: $accountId, organisation_id: $org_id}})
            ON CREATE SET
                u.id = $accountId,
                u.name = $name,
                u.email = $email,
                u.organisation_id = $org_id,
                u.created_at = datetime(),
                u.updated_at = datetime()
            ON MATCH SET
                u.email = $email,
                u.updated_at = datetime()
            WITH u, $email AS email
            OPTIONAL MATCH (usr:{self.user_label} {{email: email}})
            FOREACH (_ IN CASE WHEN usr IS NULL THEN [] ELSE [1] END |
                MERGE (usr)-[:{self.has_account_rel}]->(u)
            )
            
            // Create Access_projects node
            MERGE (ap:{self.access_projects} {{organisation_id: $org_id, user_id: $accountId}})
            ON CREATE SET
                ap.created_at = datetime(),
                ap.updated_at = datetime()
            ON MATCH SET
                ap.updated_at = datetime()
            
            // Create Assigned_tasks node
            MERGE (at:{self.assigned_tasks} {{organisation_id: $org_id, user_id: $accountId}})
            ON CREATE SET
                at.created_at = datetime(),
                at.updated_at = datetime()
            ON MATCH SET
                at.updated_at = datetime()
            
            // Create Reported_tasks node  
            MERGE (rt:{self.reported_tasks} {{organisation_id: $org_id, user_id: $accountId}})
            ON CREATE SET
                rt.created_at = datetime(),
                rt.updated_at = datetime()
            ON MATCH SET
                rt.updated_at = datetime()
            
            // Create relationships from JiraUser to the new nodes
            MERGE (u)-[:{self.has_access_rel}]->(ap)
            MERGE (u)-[:{self.has_assigned_tasks_rel}]->(at)
            MERGE (u)-[:{self.has_reported_tasks_rel}]->(rt)
            
            RETURN u, ap, at, rt;
        """
    
    @property
    def CREATE_USER_NODE_WITHOUT_EMAIL(self):
        """
        Create a User node in Neo4j without email.
        """
        return f"""
            MERGE (u:{self.jira_user_label} {{id: $accountId, organisation_id: $org_id}})
            ON CREATE SET
                u.id = $accountId,
                u.name = $name,
                u.organisation_id = $org_id,
                u.created_at = datetime(),
                u.updated_at = datetime()
            
            // Create Access_projects node
            MERGE (ap:{self.access_projects} {{id: $accountId + "_access", user_id: $accountId}})
            ON CREATE SET
                ap.created_at = datetime(),
                ap.updated_at = datetime()
            ON MATCH SET
                ap.updated_at = datetime()
            
            // Create Assigned_tasks node
            MERGE (at:{self.assigned_tasks} {{id: $accountId + "_assigned", user_id: $accountId}})
            ON CREATE SET
                at.created_at = datetime(),
                at.updated_at = datetime()
            ON MATCH SET
                at.updated_at = datetime()
            
            // Create Reported_tasks node
            MERGE (rt:{self.reported_tasks} {{id: $accountId + "_reported", user_id: $accountId}})
            ON CREATE SET
                rt.created_at = datetime(),
                rt.updated_at = datetime()
            ON MATCH SET
                rt.updated_at = datetime()
            
            // Create relationships from JiraUser to the new nodes
            MERGE (u)-[:{self.has_access_rel}]->(ap)
            MERGE (u)-[:{self.has_assigned_tasks_rel}]->(at)  
            MERGE (u)-[:{self.has_reported_tasks_rel}]->(rt)
            
            RETURN u, ap, at, rt
        """
    
    @property
    def CREATE_ISSUE_NODE_WITH_PARENT(self):
        """
        Create an Issue node in Neo4j with parent.
        """
        return f"""
        MERGE (i:{self.issue_label} {{key: $key}})
        ON CREATE SET
            i.id = $id,
            i.key = $key,
            i.project_key = $project_key,
            i.parent_key = $parent_key,
            i.issue_type = $issueType,
            i.title = $title,
            i.status = $status,
            i.priority = $priority,
            i.labels = $labels,
            i.due_date = $dueDate,
            i.progress = $progress,
            i.aggregate_progress = $aggregate_progress,
            i.storypoints = $storypoints,
            i.timespent = $timespent,
            i.updated_at = $updated,
            i.created_at = datetime()
        ON MATCH SET
            i.status = $status,
            i.priority = $priority,
            i.progress = $progress,
            i.aggregate_progress = $aggregate_progress,
            i.storypoints = $storypoints,
            i.timespent = $timespent,
            i.updated_at = $updated
        
        MERGE (parent:{self.issue_label} {{key: $parent_key}})
        MERGE (parent)-[:{self.has_parent_rel}]->(i)
        
        RETURN i
        """
    
    @property
    def CREATE_ISSUE_NODE_WITHOUT_PARENT(self):
        """
        Create an Issue node in Neo4j without parent.
        """
        return f"""
        MERGE (p:{self.project_label} {{key: $project_key}})
        MERGE (i:{self.issue_label} {{key: $key}})
        ON CREATE SET
            i.id = $id,
            i.key = $key,
            i.project_key = $project_key,
            i.issue_type = $issueType,
            i.status = $status,
            i.priority = $priority,
            i.title = $title,
            i.labels = $labels,
            i.due_date = $dueDate,
            i.progress = $progress,
            i.aggregate_progress = $aggregate_progress,
            i.storypoints = $storypoints,
            i.timespent = $timespent,
            i.updated_at = $updated,
            i.created_at = datetime()
        ON MATCH SET
            i.status = $status,
            i.priority = $priority,
            i.progress = $progress,
            i.aggregate_progress = $aggregate_progress,
            i.storypoints = $storypoints,
            i.timespent = $timespent,
            i.updated_at = $updated
        
        MERGE (p)-[:{self.has_issue_rel}]->(i)
        
        RETURN i
        """
    
    @property
    def CREATE_USER_ISSUE_RELATIONSHIP_REPORTED(self):
        """
        Create a relationship between a User and an Issue for reported tasks.
        """
        return f"""
        MATCH (u:{self.jira_user_label} {{id: $user_account_id}})
        MATCH (rt:{self.reported_tasks} {{user_id: $user_account_id}})
        MATCH (i:{self.issue_label} {{key: $issue_key}})
        MERGE (rt)-[r:{self.reported_to_rel}]->(i)
        ON CREATE SET 
            r.created_at = datetime(),
            r.account_id = $user_account_id
        RETURN u, rt, i, r
        """
    
    @property
    def CREATE_USER_ISSUE_RELATIONSHIP_ASSIGNED(self):
        """
        Create a relationship between a User and an Issue for assigned tasks.
        """
        return f"""
        MATCH (u:{self.jira_user_label} {{id: $user_account_id}})
        MATCH (at:{self.assigned_tasks} {{user_id: $user_account_id}})
        MATCH (i:{self.issue_label} {{key: $issue_key}})
        MERGE (at)-[r:{self.assigned_to_rel}]->(i)
        ON CREATE SET 
            r.created_at = datetime(),
            r.account_id = $user_account_id
        RETURN u, at, i, r
        """
    
    @property
    def CREATE_PROJECT_ACCESS_RELATIONSHIP(self):
        """
        Create a relationship between a Project and an Access_projects node.
        """
        return f"""
        MATCH (p:{self.project_label} {{key: $project_key}})
        MATCH (ap:{self.access_projects} {{user_id: $user_account_id}})
        MERGE (p)-[r:{self.has_access_rel}]->(ap)
        ON CREATE SET r.created_at = datetime()
        RETURN p, ap
        """