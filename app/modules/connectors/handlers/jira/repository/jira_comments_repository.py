"""
Repository for interacting with Jira comments in PostgreSQL database.
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid
import structlog
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, and_, or_

from app.db.postgres import db_session
from app.modules.connectors.handlers.jira.models.jira_comments import JiraComment

logger = structlog.get_logger()

class JiraCommentsRepository:
    """
    Repository for interacting with Jira comments in PostgreSQL database.
    
    This repository provides methods for storing and retrieving Jira comments.
    """
    
    def create_comment(self, 
                      org_id: str,
                      project_key: str,
                      issue_key: str,
                      comment_date: datetime,
                      comment: str,
                      user_email: Optional[str] = None,
                      user_id: Optional[str] = None) -> str:
        """
        Create a new Jira comment record in the database.
        
        Args:
            org_id: Organization ID
            project_key: Jira project key
            issue_key: Jira issue key
            comment_date: Date when the comment was created in Jira
            comment: Comment text content
            user_email: Email of the user who created the comment
            user_id: ID of the user who created the comment
            
        Returns:
            UUID of the created comment
        
        Raises:
            SQLAlchemyError: If there's an error creating the comment
        """
        try:
            comment_uuid = str(uuid.uuid4())
            jira_comment = JiraComment(
                uuid=comment_uuid,
                org_id=org_id,
                project_key=project_key,
                issue_key=issue_key,
                comment_date=comment_date,
                comment=comment,
                user_email=user_email,
                user_id=user_id
            )
            
            db_session.add(jira_comment)
            db_session.commit()
            
            logger.info(f"Created Jira comment record for issue {issue_key}", 
                       comment_uuid=comment_uuid)
            
            return comment_uuid
        except SQLAlchemyError as e:
            db_session.rollback()
            logger.error(f"Error creating Jira comment: {str(e)}", 
                        org_id=org_id, 
                        project_key=project_key, 
                        issue_key=issue_key)
            raise
    
    def create_comments_batch(self, comments: List[Dict[str, Any]]) -> List[str]:
        """
        Create multiple Jira comment records in a single transaction.
        
        Args:
            comments: List of comment dictionaries with the following keys:
                - org_id: Organization ID
                - project_key: Jira project key
                - issue_key: Jira issue key
                - comment_date: Date when the comment was created in Jira
                - comment: Comment text content
                - user_email: (Optional) Email of the user who created the comment
                - user_id: (Optional) ID of the user who created the comment
                
        Returns:
            List of UUIDs for the created comments
            
        Raises:
            SQLAlchemyError: If there's an error creating the comments
        """
        try:
            comment_uuids = []
            
            for comment_data in comments:
                comment_uuid = str(uuid.uuid4())
                jira_comment = JiraComment(
                    uuid=comment_uuid,
                    org_id=comment_data.get('org_id'),
                    project_key=comment_data.get('project_key'),
                    issue_key=comment_data.get('issue_key'),
                    comment_date=comment_data.get('comment_date'),
                    comment=comment_data.get('comment'),
                    user_email=comment_data.get('user_email'),
                    user_id=comment_data.get('user_id')
                )
                
                db_session.add(jira_comment)
                comment_uuids.append(comment_uuid)
            
            db_session.commit()
            
            logger.info(f"Created {len(comment_uuids)} Jira comment records in batch")
            
            return comment_uuids
        except SQLAlchemyError as e:
            db_session.rollback()
            logger.error(f"Error creating Jira comments batch: {str(e)}")
            raise
    
    def get_comments_by_issue(self, org_id: str, issue_key: str) -> List[JiraComment]:
        """
        Get all comments for a specific issue.
        
        Args:
            org_id: Organization ID
            issue_key: Jira issue key
            
        Returns:
            List of JiraComment objects
        """
        try:
            comments = db_session.query(JiraComment).filter(
                JiraComment.org_id == org_id,
                JiraComment.issue_key == issue_key
            ).order_by(JiraComment.comment_date).all()
            
            return comments
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving comments for issue {issue_key}: {str(e)}", 
                        org_id=org_id)
            return []
    
    def get_comments_by_project(self, 
                               org_id: str, 
                               project_key: str, 
                               limit: int = 100, 
                               offset: int = 0) -> List[JiraComment]:
        """
        Get comments for a specific project with pagination.
        
        Args:
            org_id: Organization ID
            project_key: Jira project key
            limit: Maximum number of comments to return
            offset: Number of comments to skip
            
        Returns:
            List of JiraComment objects
        """
        try:
            comments = db_session.query(JiraComment).filter(
                JiraComment.org_id == org_id,
                JiraComment.project_key == project_key
            ).order_by(desc(JiraComment.comment_date)).limit(limit).offset(offset).all()
            
            return comments
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving comments for project {project_key}: {str(e)}", 
                        org_id=org_id)
            return []
    
    def get_comments_by_user(self, 
                            org_id: str, 
                            user_id: str, 
                            limit: int = 100, 
                            offset: int = 0) -> List[JiraComment]:
        """
        Get comments created by a specific user with pagination.
        
        Args:
            org_id: Organization ID
            user_id: Jira user ID
            limit: Maximum number of comments to return
            offset: Number of comments to skip
            
        Returns:
            List of JiraComment objects
        """
        try:
            comments = db_session.query(JiraComment).filter(
                JiraComment.org_id == org_id,
                JiraComment.user_id == user_id
            ).order_by(desc(JiraComment.comment_date)).limit(limit).offset(offset).all()
            
            return comments
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving comments for user {user_id}: {str(e)}", 
                        org_id=org_id)
            return []
    
    def get_comments_by_date_range(self, 
                                  org_id: str, 
                                  start_date: datetime, 
                                  end_date: datetime,
                                  project_key: Optional[str] = None,
                                  limit: int = 100, 
                                  offset: int = 0) -> List[JiraComment]:
        """
        Get comments created within a specific date range with pagination.
        
        Args:
            org_id: Organization ID
            start_date: Start date for the range
            end_date: End date for the range
            project_key: (Optional) Filter by project key
            limit: Maximum number of comments to return
            offset: Number of comments to skip
            
        Returns:
            List of JiraComment objects
        """
        try:
            query = db_session.query(JiraComment).filter(
                JiraComment.org_id == org_id,
                JiraComment.comment_date >= start_date,
                JiraComment.comment_date <= end_date
            )
            
            if project_key:
                query = query.filter(JiraComment.project_key == project_key)
                
            comments = query.order_by(desc(JiraComment.comment_date)).limit(limit).offset(offset).all()
            
            return comments
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving comments by date range: {str(e)}", 
                        org_id=org_id, 
                        start_date=start_date, 
                        end_date=end_date)
            return []
    
    def delete_comments_by_issue(self, org_id: str, issue_key: str) -> int:
        """
        Delete all comments for a specific issue.
        
        Args:
            org_id: Organization ID
            issue_key: Jira issue key
            
        Returns:
            Number of comments deleted
        """
        try:
            count = db_session.query(JiraComment).filter(
                JiraComment.org_id == org_id,
                JiraComment.issue_key == issue_key
            ).delete()
            
            db_session.commit()
            
            logger.info(f"Deleted {count} comments for issue {issue_key}", 
                       org_id=org_id)
            
            return count
        except SQLAlchemyError as e:
            db_session.rollback()
            logger.error(f"Error deleting comments for issue {issue_key}: {str(e)}", 
                        org_id=org_id)
            return 0
    
    def delete_comments_by_project(self, org_id: str, project_key: str) -> int:
        """
        Delete all comments for a specific project.
        
        Args:
            org_id: Organization ID
            project_key: Jira project key
            
        Returns:
            Number of comments deleted
        """
        try:
            count = db_session.query(JiraComment).filter(
                JiraComment.org_id == org_id,
                JiraComment.project_key == project_key
            ).delete()
            
            db_session.commit()
            
            logger.info(f"Deleted {count} comments for project {project_key}", 
                       org_id=org_id)
            
            return count
        except SQLAlchemyError as e:
            db_session.rollback()
            logger.error(f"Error deleting comments for project {project_key}: {str(e)}", 
                        org_id=org_id)
            return 0